#!/bin/bash
set -e

echo "🚀 Starting MCP Server Development Container"

# Set up environment
export PYTHONPATH="/home/<USER>/repo/qw-mono/src:$PYTHONPATH"
export BUILD_VERSION="${BUILD_VERSION:-0.0.0.0}"
export BUILD_COMMIT="${BUILD_COMMIT:-$(echo '0' | head -c 40)}"

echo "📦 Environment setup:"
echo "  - PYTHONPATH: $PYTHONPATH"
echo "  - BUILD_VERSION: $BUILD_VERSION"
echo "  - BUILD_COMMIT: $BUILD_COMMIT"

# Change to the repo directory
cd /home/<USER>/repo

# Install Python dependencies if needed
echo "📦 Installing Python dependencies..."
# Dependencies are already installed in the Docker image

# Start the MCP server
echo "🚀 Starting FastMCP Server..."
exec python3 -m qw_mcp_server.mcp_server_entrypoint \
    --qw-mono-config /home/<USER>/repo/dev_data/app.yaml
