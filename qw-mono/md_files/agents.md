# AI Agent Architecture Analysis and Refactoring Proposal

## Phase 1: Codebase Analysis and Documentation

### Complete Inventory of AI Agents

#### 1. **qw_agent_service** - Standalone FastAPI Agent Service
**Location**: `qw-mono/src/qw_agent_service/`
**Purpose**: Dedicated FastAPI service for AI agent processing with MCP integration
**Current Functionality**:
- **MCPEnhancedAgent** (`mcp_enhanced_agent.py`): Main agent with FastMCP client integration
- **MinimalAgent** (`minimal_agent.py`): Basic RouterAgent without specialized agents
- **AgentService** (`service.py`): Service orchestration and session management
- **FastAPI Application** (`main.py`): HTTP API endpoints at `/api/v1/agent/`
- **Authentication**: Session-based auth via runtime service validation
- **Configuration**: YAML-based config with environment overrides

**Key Features**:
- MCP client integration for tool execution
- Session token management for API authentication
- Health check endpoints for container orchestration
- Async processing with lifespan management

#### 2. **qw_trunk Agent System** - Legacy Falcon-based Agent Architecture
**Location**: `qw-mono/src/qw_trunk/service/agent/`
**Purpose**: Original agent system integrated with Falcon API
**Current Functionality**:

**Router Agent** (`agents/router_agent/router_agent.py`):
- Main routing agent that delegates to specialized agents
- Uses global agent registry for agent discovery
- Integrated with Falcon API via `ProcessAgentPromptController`

**Specialized Agents** (`agents/specialized_agents/`):
- **DocumentIntelligenceAgent**: Cross-document analysis and auditing
- **InspectionPlanBuilderAgent**: PMI and inspection plan management
- **Registration system**: Global agent registry with dependency injection

**Tools System** (`tools/`):
- **Common Tools**: Context and action discovery tools
- **Special Tools**: Domain-specific tools (PMI, drawing analysis, material certificates)
- **Tool Registry**: Centralized tool management

**MCP Server** (`mcp/`):
- **FastMCPServerService**: Exposes internal APIs as MCP tools
- **Service Account Authentication**: Bearer token authentication
- **OpenAPI Integration**: Auto-generates tools from OpenAPI spec

#### 3. **Material Certificate Analysis Agents** - Specialized Multi-Agent System
**Location**: `qw-mono/src/qw_material_certificate_analysis/agents/`
**Purpose**: Multi-agent system for material certificate processing
**Current Functionality**:
- **MetadataAgent**: Conversational metadata collection
- **HeaderDetailsAgent**: Header/batch extraction
- **ChemicalCompositionAgent**: Chemical analysis extraction
- **MechanicalPropertiesAgent**: Mechanical properties extraction
- **AgenticAnalysisService**: Multi-agent orchestration service

**Key Features**:
- Uses Google Gemini models via PydanticAI
- Database-inspired foreign key design
- Vision API integration for document processing
- Async agent lifecycle management

#### 4. **Drawing Toolkit OCR Agent** - PMI Processing Agent
**Location**: `qw-mono/src/qw_drawing_toolkit_ocr/post_processing/agent_processor.py`
**Purpose**: LLM-based agent for processing PMI blocks from OCR results
**Current Functionality**:
- PydanticAI framework with async processing
- OpenAI model integration
- Image processing and analysis
- PMI block extraction and validation

### Agent Interactions and External Systems

#### Current Agent Communication Patterns

1. **qw_agent_service ↔ MCP Server**:
   - FastMCP client connects to MCP server at `http://mcp.docker.localhost`
   - Session token authentication for API calls
   - Tool discovery and execution via MCP protocol

2. **qw_trunk Router Agent ↔ Specialized Agents**:
   - Global agent registry for agent discovery
   - Direct method calls via dependency injection
   - Context passing through AgentContext objects

3. **Falcon API ↔ qw_trunk Agent System**:
   - `ProcessAgentPromptController` at `/api/v1/agent/process-prompt`
   - Session-based authentication
   - JSON request/response format

4. **FastAPI ↔ qw_agent_service**:
   - Endpoints at `/api/v1/agent/process-prompt` and `/api/v1/agent/health`
   - Traefik routing based on path prefix
   - Async request processing

#### External System Dependencies

1. **Authentication Systems**:
   - Keycloak for identity management
   - Session token validation via runtime service
   - Service account tokens for MCP server authentication

2. **Storage Systems**:
   - PostgreSQL for session and data persistence
   - S3-compatible storage (MinIO) for file operations
   - Redis-like caching for session management

3. **Container Orchestration**:
   - Docker Compose for development environment
   - Traefik for reverse proxy and load balancing
   - Container networking for service communication

### MCP (Model Context Protocol) Implementation

#### Current MCP Architecture

**MCP Server** (`qw_trunk/service/agent/mcp/`):
- **FastMCPServerService**: Standalone MCP server exposing internal APIs
- **Container**: `qw-mono-dev-mcp-server` at `mcp.docker.localhost`
- **Transport**: Streamable HTTP at root path for MCP Inspector compatibility
- **Authentication**: Service account tokens + session-based auth
- **Tool Generation**: Auto-generates tools from OpenAPI specification

**MCP Client** (`qw_agent_service/mcp_enhanced_agent.py`):
- **FastMCP Client**: Connects to MCP server for tool execution
- **Health Checking**: Validates MCP server availability before setup
- **Tool Discovery**: Dynamic discovery of available MCP tools
- **Session Management**: Handles session token updates for authentication

#### MCP Tool Execution Flow
1. Agent receives user prompt
2. Agent determines need for tool execution
3. Agent calls `call_mcp_tool` with tool name and arguments
4. MCP client sends request to MCP server
5. MCP server executes tool via internal API call
6. Response returned through MCP protocol
7. Agent incorporates tool result into response

### Unused and Deprecated Code Analysis

#### Potentially Unused Components

1. **Legacy Agent Patterns**:
   - Some tools in `qw_trunk/service/agent/tools/` may be unused
   - Old agent registration patterns that predate current architecture

2. **Duplicate Functionality**:
   - Both `qw_agent_service` and `qw_trunk` agent systems serve similar purposes
   - Overlapping session management implementations
   - Duplicate model definitions between services

3. **Development Artifacts**:
   - Test files and experimental code in agent directories
   - Commented-out code in agent implementations
   - TODO comments indicating incomplete features

#### Configuration Inconsistencies

1. **Multiple Configuration Systems**:
   - `qw_agent_service` uses its own config models
   - `qw_trunk` uses different configuration patterns
   - MCP server has separate configuration structure

2. **Environment Variable Handling**:
   - Inconsistent environment variable naming
   - Some services use YAML overrides, others use direct env vars
   - Missing configuration validation in some components

### Current Architecture Diagram

```mermaid
graph TB
    subgraph "Frontend"
        UI[Angular UI]
    end

    subgraph "API Gateway"
        Traefik[Traefik Reverse Proxy]
    end

    subgraph "Agent Services"
        FastAPI[qw_agent_service<br/>FastAPI]
        Falcon[qw_trunk<br/>Falcon API]
        MCP[MCP Server<br/>FastMCP]
    end

    subgraph "Agent Implementations"
        MCPAgent[MCP Enhanced Agent]
        RouterAgent[Router Agent]
        SpecAgents[Specialized Agents<br/>- Document Intelligence<br/>- Inspection Plan Builder]
        MaterialAgents[Material Certificate<br/>Analysis Agents]
        OCRAgent[Drawing OCR<br/>PMI Agent]
    end

    subgraph "Tools & Services"
        Tools[Agent Tools<br/>- Common Tools<br/>- Special Tools]
        Services[Business Services<br/>- File Service<br/>- Drawing Service<br/>- Material Service]
    end

    subgraph "Infrastructure"
        DB[(PostgreSQL)]
        S3[(MinIO S3)]
        Auth[Keycloak]
    end

    UI --> Traefik
    Traefik --> FastAPI
    Traefik --> Falcon
    Traefik --> MCP

    FastAPI --> MCPAgent
    Falcon --> RouterAgent
    RouterAgent --> SpecAgents

    MCPAgent --> MCP
    MCP --> Services

    SpecAgents --> Tools
    Tools --> Services

    MaterialAgents --> Services
    OCRAgent --> Services

    Services --> DB
    Services --> S3
    Services --> Auth
```

## Phase 2: Architecture Assessment and Refactoring Proposal

### Critical Analysis of Current Architecture

#### Major Inefficiencies and Issues

1. **Architectural Fragmentation**:
   - **Dual Agent Systems**: Both `qw_agent_service` and `qw_trunk` provide agent functionality with overlapping responsibilities
   - **Inconsistent Patterns**: Different authentication, configuration, and deployment patterns across services
   - **Technology Stack Mixing**: FastAPI and Falcon APIs serving similar purposes
   - **Duplicate Code**: Model definitions, session management, and authentication logic duplicated

2. **Complex Routing and Communication**:
   - **Traffic Steering Complexity**: Traefik routing based on path prefixes creates maintenance overhead
   - **Multiple Entry Points**: Users can access agents through different endpoints with different behaviors
   - **MCP Protocol Overhead**: Additional network hops and protocol translation for simple tool execution
   - **Session Management Duplication**: Multiple session validation implementations

3. **Scalability and Maintenance Issues**:
   - **Tight Coupling**: Specialized agents tightly coupled to specific service implementations
   - **Global State**: Agent registry uses global state making testing and isolation difficult
   - **Configuration Sprawl**: Multiple configuration systems with different patterns and validation
   - **Container Proliferation**: Separate containers for similar functionality increases deployment complexity

4. **Development Experience Problems**:
   - **Cognitive Load**: Developers must understand multiple agent systems and their interactions
   - **Testing Complexity**: Integration testing requires multiple services and complex setup
   - **Debugging Difficulty**: Request flow spans multiple services making issue diagnosis challenging
   - **Inconsistent APIs**: Different request/response formats across agent endpoints

#### Specific Technical Debt

1. **Authentication Inconsistencies**:
   - MCP server uses both service account tokens and session-based auth
   - Different session validation timeouts and caching strategies
   - Inconsistent error handling for authentication failures

2. **Tool Execution Patterns**:
   - Some tools execute directly via service injection
   - Others go through MCP protocol with HTTP overhead
   - Inconsistent error handling and timeout management

3. **Agent Lifecycle Management**:
   - Material certificate agents create fresh instances per request
   - Other agents maintain persistent state
   - Inconsistent resource cleanup and memory management

### Detailed Refactoring Proposal

#### Goal 1: Consolidate User Interactions through qw_agent_service

**Rationale**: Establish `qw_agent_service` as the single entry point for all LLM interactions to eliminate routing complexity and provide consistent user experience.

**Implementation Strategy**:

1. **Migrate Falcon Agent Endpoint**:
   - Deprecate `ProcessAgentPromptController` in `qw_pfoertner`
   - Update Traefik routing to direct all `/api/v1/agent/*` requests to `qw_agent_service`
   - Implement backward compatibility layer for existing clients

2. **Standardize Request/Response Models**:
   - Use `qw_agent_service/models.py` as the canonical model definitions
   - Update all agent implementations to use consistent models
   - Implement model validation and error handling

3. **Unified Session Management**:
   - Consolidate session management in `qw_agent_service`
   - Implement consistent session validation and caching
   - Remove duplicate session handling from `qw_trunk`

**Benefits**:
- Single API contract for all agent interactions
- Simplified client implementation and testing
- Consistent authentication and error handling
- Reduced infrastructure complexity

#### Goal 2: Integrate router_agent with mcp_client

**Rationale**: Combine the routing intelligence of `router_agent` with the tool execution capabilities of `mcp_client` to create a unified agent orchestration system.

**Implementation Strategy**:

1. **Enhanced Router Agent**:
   ```python
   class UnifiedRouterAgent:
       def __init__(self, mcp_client: MCPClient, specialized_agents: Dict[str, BaseAgent]):
           self.mcp_client = mcp_client
           self.specialized_agents = specialized_agents
           self.agent_registry = AgentRegistry()

       async def process(self, prompt: str, context: AgentContext) -> AgentResponse:
           # Determine if specialized agent is needed
           agent_decision = await self._route_to_agent(prompt, context)

           if agent_decision.use_specialized_agent:
               return await self._delegate_to_specialized_agent(
                   agent_decision.agent_name, prompt, context
               )
           else:
               # Use MCP tools for general queries
               return await self._process_with_mcp_tools(prompt, context)
   ```

2. **MCP Tool Integration**:
   - Expose specialized agent capabilities as MCP tools
   - Implement tool discovery and registration system
   - Handle tool execution errors and timeouts consistently

3. **Agent Registry Modernization**:
   - Replace global registry with dependency-injected registry
   - Implement agent health checking and failover
   - Add agent performance monitoring and metrics

**Benefits**:
- Unified agent orchestration with intelligent routing
- Consistent tool execution patterns
- Better error handling and monitoring
- Simplified agent development and testing

#### Goal 3: Plugin-based System for Agent Registration

**Rationale**: Create a flexible plugin system that allows easy registration and management of specialized agents while maintaining clear separation of concerns.

**Implementation Strategy**:

1. **Agent Plugin Interface**:
   ```python
   class AgentPlugin(ABC):
       @property
       @abstractmethod
       def name(self) -> str:
           """Unique agent name for registration."""
           pass

       @property
       @abstractmethod
       def description(self) -> str:
           """Agent description for routing decisions."""
           pass

       @property
       @abstractmethod
       def capabilities(self) -> List[str]:
           """List of capabilities this agent provides."""
           pass

       @abstractmethod
       async def process(self, prompt: str, context: AgentContext) -> AgentResponse:
           """Process a user prompt and return response."""
           pass

       @abstractmethod
       async def health_check(self) -> bool:
           """Check if agent is healthy and ready to process requests."""
           pass
   ```

2. **Plugin Registry System**:
   ```python
   class AgentPluginRegistry:
       def __init__(self, lf: LogFactory):
           self.plugins: Dict[str, AgentPlugin] = {}
           self.logger = lf.get_logger(__name__)

       def register_plugin(self, plugin: AgentPlugin) -> None:
           """Register an agent plugin."""
           self.plugins[plugin.name] = plugin
           self.logger.info(f"Registered agent plugin: {plugin.name}")

       async def discover_plugins(self, plugin_dir: Path) -> None:
           """Auto-discover and load plugins from directory."""
           # Implementation for dynamic plugin loading
           pass

       def get_plugin(self, name: str) -> Optional[AgentPlugin]:
           """Get plugin by name."""
           return self.plugins.get(name)

       def get_plugins_by_capability(self, capability: str) -> List[AgentPlugin]:
           """Get all plugins that provide a specific capability."""
           return [p for p in self.plugins.values() if capability in p.capabilities]
   ```

3. **Configuration-driven Registration**:
   ```yaml
   agent_plugins:
     - name: "document_intelligence"
       enabled: true
       config:
         model_name: "gpt-4o"
         timeout: 30.0
     - name: "inspection_plan_builder"
       enabled: true
       config:
         model_name: "gpt-4o"
         timeout: 45.0
   ```

**Benefits**:
- Easy addition of new specialized agents
- Configuration-driven agent management
- Clear separation between agent logic and registration
- Simplified testing and development

#### Goal 4: MCP Server Communication for Specialized Agents

**Rationale**: Enable specialized agents to communicate with MCP servers when configured, providing consistent tool execution and better resource management.

**Implementation Strategy**:

1. **Agent-MCP Bridge**:
   ```python
   class MCPAgentBridge:
       def __init__(self, mcp_client: MCPClient, lf: LogFactory):
           self.mcp_client = mcp_client
           self.logger = lf.get_logger(__name__)

       async def execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
           """Execute MCP tool and return result."""
           try:
               result = await self.mcp_client.call_tool(tool_name, arguments)
               return self._parse_tool_result(result)
           except Exception as e:
               self.logger.error(f"MCP tool execution failed: {e}")
               raise

       async def discover_tools(self) -> List[str]:
           """Discover available MCP tools."""
           return await self.mcp_client.list_tools()
   ```

2. **Tool Execution Capabilities**:
   - Implement consistent tool execution patterns across all agents
   - Add tool result caching and optimization
   - Handle tool execution timeouts and retries
   - Provide tool execution metrics and monitoring

3. **MCP Server Configuration**:
   ```yaml
   mcp_servers:
     internal:
       url: "http://qw-mono-dev-mcp:8000"
       authentication:
         type: "service_account"
         token: "${SERVICE_ACCOUNT_TOKEN}"
     external:
       - name: "custom_tools"
         url: "http://external-mcp-server:8000"
         authentication:
           type: "api_key"
           key: "${EXTERNAL_API_KEY}"
   ```

**Benefits**:
- Consistent tool execution across all agents
- Better resource management and caching
- Support for external MCP servers
- Improved monitoring and debugging

### Architectural Recommendation: Agent Location Strategy

After analyzing the current architecture and considering separation of concerns, I recommend the following distribution strategy:

#### **Recommendation: Hybrid Approach with Clear Boundaries**

**Core Principle**: Tools ↔ Agents ↔ Endpoints (Decoupled Architecture)

1. **qw_agent_service**: Agent Orchestration and API Layer
   - **UnifiedRouterAgent**: Main orchestration logic
   - **AgentPluginRegistry**: Plugin management and discovery
   - **MCPAgentBridge**: Tool execution coordination
   - **FastAPI Endpoints**: HTTP API and authentication
   - **Session Management**: User session and context management

2. **qw_trunk/service/agent/agents**: Specialized Business Logic Agents
   - **Domain-specific agents**: DocumentIntelligenceAgent, InspectionPlanBuilderAgent
   - **Business logic**: Complex workflows and domain expertise
   - **Service integration**: Direct access to business services when needed
   - **Plugin implementations**: Implement AgentPlugin interface

3. **qw_trunk/service/agent/tools**: Reusable Tool Components
   - **Common tools**: Context, action discovery, utility functions
   - **Special tools**: Domain-specific tool implementations
   - **MCP tool wrappers**: Bridge between tools and MCP protocol
   - **Tool registry**: Centralized tool management and discovery

**Rationale for Hybrid Approach**:

1. **Clear Separation of Concerns**:
   - API layer handles HTTP, authentication, and routing
   - Business agents contain domain expertise and workflows
   - Tools provide reusable, composable functionality

2. **Maintainability**:
   - Business logic remains close to related services
   - API layer can evolve independently
   - Tools can be shared across multiple agents

3. **Scalability**:
   - Agent orchestration can be scaled independently
   - Business agents can be deployed as separate services if needed
   - Tools can be cached and optimized centrally

4. **Development Experience**:
   - Clear boundaries for different types of development work
   - Business domain experts work in familiar service layer
   - API developers focus on orchestration and user experience

## Phase 3: Implementation Roadmap

### Step-by-Step Implementation Plan

#### **Phase 3.1: Foundation and Consolidation (Weeks 1-2)**

**Priority: High Impact, Low Complexity**

1. **Standardize Models and Interfaces**:
   - [ ] Create canonical model definitions in `qw_agent_service/models.py`
   - [ ] Update all agent implementations to use standard models
   - [ ] Implement model validation and error handling
   - [ ] Create shared authentication models

2. **Consolidate Configuration**:
   - [ ] Create unified configuration schema for all agent services
   - [ ] Implement configuration validation and defaults
   - [ ] Update Docker Compose and deployment scripts
   - [ ] Document configuration options and environment variables

3. **Implement Agent Plugin Interface**:
   - [ ] Define `AgentPlugin` abstract base class
   - [ ] Create `AgentPluginRegistry` implementation
   - [ ] Update existing specialized agents to implement plugin interface
   - [ ] Add plugin health checking and monitoring

**Deliverables**:
- Unified model definitions
- Plugin interface specification
- Updated configuration system
- Plugin registry implementation

#### **Phase 3.2: Router Agent Integration (Weeks 3-4)**

**Priority: High Impact, Medium Complexity**

1. **Create UnifiedRouterAgent**:
   - [ ] Implement enhanced router agent with MCP integration
   - [ ] Add intelligent routing logic based on prompt analysis
   - [ ] Implement fallback mechanisms for agent failures
   - [ ] Add request/response logging and metrics

2. **MCP Client Enhancement**:
   - [ ] Improve MCP client error handling and retries
   - [ ] Add tool result caching and optimization
   - [ ] Implement tool execution timeouts and monitoring
   - [ ] Add support for multiple MCP servers

3. **Agent Registry Modernization**:
   - [ ] Replace global registry with dependency injection
   - [ ] Implement agent lifecycle management
   - [ ] Add agent performance monitoring
   - [ ] Create agent discovery and health checking

**Deliverables**:
- UnifiedRouterAgent implementation
- Enhanced MCP client with monitoring
- Modernized agent registry
- Performance monitoring dashboard

#### **Phase 3.3: API Consolidation (Weeks 5-6)**

**Priority: High Impact, High Complexity**

1. **Migrate Falcon Endpoints**:
   - [ ] Update Traefik routing to direct all agent requests to FastAPI
   - [ ] Implement backward compatibility layer for existing clients
   - [ ] Add request/response transformation if needed
   - [ ] Update client libraries and documentation

2. **Session Management Unification**:
   - [ ] Consolidate session management in `qw_agent_service`
   - [ ] Implement consistent session validation and caching
   - [ ] Remove duplicate session handling from `qw_trunk`
   - [ ] Add session monitoring and cleanup

3. **Authentication Standardization**:
   - [ ] Implement consistent authentication patterns
   - [ ] Add service account token management
   - [ ] Update MCP server authentication
   - [ ] Add authentication monitoring and alerting

**Deliverables**:
- Single agent API endpoint
- Unified session management
- Consistent authentication system
- Updated client documentation

#### **Phase 3.4: Tool System Enhancement (Weeks 7-8)**

**Priority: Medium Impact, Medium Complexity**

1. **MCP Tool Integration**:
   - [ ] Expose specialized agent capabilities as MCP tools
   - [ ] Implement tool discovery and registration system
   - [ ] Add tool execution error handling and timeouts
   - [ ] Create tool performance monitoring

2. **Tool Registry Enhancement**:
   - [ ] Implement centralized tool management
   - [ ] Add tool versioning and compatibility checking
   - [ ] Create tool documentation and discovery API
   - [ ] Add tool usage analytics

3. **Agent-Tool Bridge**:
   - [ ] Implement MCPAgentBridge for consistent tool execution
   - [ ] Add tool result caching and optimization
   - [ ] Create tool execution metrics and monitoring
   - [ ] Add support for external MCP servers

**Deliverables**:
- Enhanced tool system with MCP integration
- Centralized tool registry
- Tool performance monitoring
- External MCP server support

#### **Phase 3.5: Specialized Agent Migration (Weeks 9-10)**

**Priority: Medium Impact, Low Complexity**

1. **Plugin System Implementation**:
   - [ ] Implement configuration-driven plugin loading
   - [ ] Create plugin development documentation
   - [ ] Add plugin testing framework
   - [ ] Implement plugin hot-reloading for development

2. **Agent Migration**:
   - [ ] Migrate DocumentIntelligenceAgent to plugin system
   - [ ] Migrate InspectionPlanBuilderAgent to plugin system
   - [ ] Update material certificate analysis agents
   - [ ] Migrate drawing OCR agent

3. **Testing and Validation**:
   - [ ] Create comprehensive integration tests
   - [ ] Implement agent performance benchmarks
   - [ ] Add end-to-end testing scenarios
   - [ ] Create load testing and stress testing

**Deliverables**:
- Complete plugin system implementation
- All agents migrated to new architecture
- Comprehensive test suite
- Performance benchmarks

### Potential Breaking Changes and Migration Strategies

#### **Breaking Changes**

1. **API Endpoint Changes**:
   - **Change**: Single agent endpoint instead of multiple services
   - **Impact**: Client applications need to update endpoint URLs
   - **Migration**: Implement backward compatibility layer with request forwarding

2. **Request/Response Model Changes**:
   - **Change**: Standardized models across all agent interactions
   - **Impact**: Client libraries may need updates for new model structure
   - **Migration**: Implement model transformation layer for backward compatibility

3. **Configuration Changes**:
   - **Change**: Unified configuration schema
   - **Impact**: Deployment scripts and environment variables need updates
   - **Migration**: Provide configuration migration tool and documentation

4. **Authentication Changes**:
   - **Change**: Consistent authentication patterns across all services
   - **Impact**: Service account tokens and session handling may change
   - **Migration**: Implement gradual migration with dual authentication support

#### **Migration Strategies**

1. **Phased Rollout**:
   - Deploy new architecture alongside existing system
   - Gradually migrate clients to new endpoints
   - Monitor performance and error rates during migration
   - Remove old system after successful migration

2. **Feature Flags**:
   - Use configuration flags to enable/disable new features
   - Allow rollback to previous behavior if issues arise
   - Gradually enable new features as they are validated
   - Remove feature flags after stable deployment

3. **Backward Compatibility**:
   - Maintain old API endpoints during transition period
   - Implement request/response transformation layers
   - Provide clear deprecation timeline and migration guides
   - Monitor usage of deprecated endpoints

4. **Testing Strategy**:
   - Implement comprehensive integration tests
   - Create performance benchmarks for comparison
   - Use canary deployments for gradual rollout
   - Monitor error rates and performance metrics

### Testing Approaches for New Architecture

#### **Unit Testing Strategy**

1. **Agent Plugin Testing**:
   ```python
   class TestAgentPlugin:
       async def test_plugin_interface_compliance(self):
           """Test that plugin implements required interface."""
           plugin = DocumentIntelligencePlugin()
           assert hasattr(plugin, 'name')
           assert hasattr(plugin, 'process')
           assert await plugin.health_check()

       async def test_plugin_processing(self):
           """Test plugin processing functionality."""
           plugin = DocumentIntelligencePlugin()
           context = AgentContext(user_id=1, session_id=uuid4())
           response = await plugin.process("analyze document", context)
           assert isinstance(response, AgentResponse)
   ```

2. **MCP Integration Testing**:
   ```python
   class TestMCPIntegration:
       async def test_mcp_tool_execution(self):
           """Test MCP tool execution."""
           bridge = MCPAgentBridge(mock_mcp_client, log_factory)
           result = await bridge.execute_tool("get_file", {"file_id": "123"})
           assert result is not None

       async def test_mcp_error_handling(self):
           """Test MCP error handling."""
           bridge = MCPAgentBridge(failing_mcp_client, log_factory)
           with pytest.raises(MCPToolExecutionError):
               await bridge.execute_tool("invalid_tool", {})
   ```

#### **Integration Testing Strategy**

1. **End-to-End API Testing**:
   ```python
   class TestAgentAPI:
       async def test_agent_prompt_processing(self):
           """Test complete agent prompt processing flow."""
           async with AsyncClient(app=app, base_url="http://test") as client:
               response = await client.post("/api/v1/agent/process-prompt", json={
                   "prompt": "Create an inspection plan",
                   "context": {"user_id": 1}
               })
               assert response.status_code == 200
               data = response.json()
               assert "message" in data
               assert "actions" in data
   ```

2. **Multi-Service Integration Testing**:
   ```python
   class TestServiceIntegration:
       async def test_agent_service_integration(self):
           """Test integration between agent service and business services."""
           # Test that agents can successfully call business services
           # through MCP tools and return expected results
           pass
   ```

#### **Performance Testing Strategy**

1. **Load Testing**:
   - Use tools like Locust or Artillery for load testing
   - Test concurrent agent requests and response times
   - Monitor resource usage during high load
   - Validate that system scales appropriately

2. **Stress Testing**:
   - Test system behavior under extreme load
   - Validate error handling and recovery mechanisms
   - Test resource limits and memory usage
   - Ensure graceful degradation under stress

3. **Performance Benchmarking**:
   - Establish baseline performance metrics
   - Compare new architecture performance to current system
   - Monitor key metrics: response time, throughput, error rate
   - Set performance targets and alerts

#### **Monitoring and Observability**

1. **Application Metrics**:
   - Agent request/response times
   - Tool execution performance
   - Error rates and types
   - Session management metrics

2. **Infrastructure Metrics**:
   - Container resource usage
   - Network latency between services
   - Database connection pool usage
   - Cache hit rates and performance

3. **Business Metrics**:
   - Agent usage patterns
   - User satisfaction metrics
   - Feature adoption rates
   - Agent accuracy and effectiveness

---

This comprehensive refactoring proposal provides a clear path toward a more maintainable, scalable, and efficient AI agent architecture while preserving existing functionality and providing smooth migration paths for all stakeholders.
