"""
Drawing OCR Service module.
This module provides functionality to process drawings with OCR, handling
tile fetching, grouping, OCR processing, and result collection.
"""
import asyncio
import concurrent.futures
import io
import json
import time
import traceback
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path, PurePosixPath
from typing import Any, Dict, List, Mapping, Optional, Tuple

import cv2
import numpy as np
from numpy.typing import NDArray
from PIL import Image
from pydantic import BaseModel
from pydantic.dataclasses import dataclass

from qw_basic_s3.interface import S3Storage
from qw_drawing_toolkit_ocr.bbox_detection.models import Detection, OcrResult
from qw_drawing_toolkit_ocr.bbox_detection.rapid_ocr_engine import RapidOcrEngine
from qw_drawing_toolkit_ocr.bbox_detection.tile_manager import Tile, TileData, TileGroupManager
from qw_drawing_toolkit_ocr.bbox_detection.utils import visualize_text_regions
from qw_drawing_toolkit_ocr.post_processing.agent_processor import PmiAgentProcessor
from qw_drawing_toolkit_ocr.post_processing.config import PMIAnalysisSettings
from qw_drawing_toolkit_ocr.post_processing.material_extractor import MaterialExtractor
from qw_drawing_toolkit_ocr.post_processing.models import DrawingAnalysisResult, PMIMetadata
from qw_drawing_toolkit_ocr.post_processing.post_process_results import PostProcessResults
from qw_drawing_toolkit_ocr.post_processing.standards_extractor import StandardsExtractor
from qw_drawing_toolkit_ocr.post_processing.utils import visualize_pmi_results
from qw_drawing_toolkit_ocr.pre_processing.models import DocumentText
from qw_drawing_toolkit_ocr.pre_processing.pmi_processor import PmiProcessor
from qw_drawing_toolkit_ocr.pre_processing.utils import visualize_pre_processed_results
from qw_drawing_toolkit_ocr.pre_processing.vision_processor import VisionProcessor
from qw_log_interface import NO_LOG_FACTORY, NO_LOGGER, LogFactory, Logger

Dimensions = Tuple[int, int]
TilePosition = Tuple[int, int]


class TileInfo(BaseModel):
    """Type definition for tile information dictionary"""

    tile_obj_id: str
    tile_x: int
    tile_y: int


@dataclass
class OcrResultData:
    """Data class for OCR results to ensure proper typing"""

    processing_time: float
    ocr_processing_time: float
    num_tile_groups: int
    num_tiles: int
    num_detections: int
    results: Mapping[str, List[Dict[str, Any]]]
    canvas_width: int = 0
    canvas_height: int = 0


class DrawingOcrService:
    """
    Service class for orchestrating OCR processing of drawings.
    """

    def __init__(
        self,
        s3_storage: S3Storage,
        group_width: int = 2,
        group_height: int = 2,
        slide_step_x: int = 1,
        slide_step_y: int = 1,
        max_workers: int = 2,
        max_ocr_workers: int = 2,
        rotations: List[int] = [0, 90],
        google_api_key: str = "",  # Bring your own api when testing with CLI
        openai_api_key: str = "",  # Bring your own api when testing with CLI
        save_images_for_debugging: bool = False,  # True for debugging
        save_json_for_debugging: bool = False,  # True for debugging
        logger: Logger = NO_LOGGER,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the Drawing OCR Service.

        Args:
            s3_storage: S3Storage instance for fetching tiles
            group_width: Number of tiles in X direction for each group
            group_height: Number of tiles in Y direction for each group
            slide_step_x: Number of tiles to slide in X direction
            slide_step_y: Number of tiles to slide in Y direction
            max_workers: Maximum number of concurrent workers for tile fetching
            max_ocr_workers: Maximum number of concurrent workers for OCR processing
            rotations: List of rotation angles to apply during OCR. Clockwise.
            ...
        """
        self.s3_storage = s3_storage
        self.logger = logger
        self.lf = lf
        self.max_workers = max_workers
        self.max_ocr_workers = max_ocr_workers
        self.rotations = rotations
        self.save_images_for_debugging = save_images_for_debugging
        self.save_json_for_debugging = save_json_for_debugging

        # Initialize test_output_dir to None by default
        self.output_dir = None
        self.test_output_dir = None

        # Set output directory for tests if debugging is enabled
        if save_images_for_debugging or save_json_for_debugging:
            self.output_dir = Path.cwd() / "ocr_test_results"
            self.output_dir.mkdir(parents=True, exist_ok=True)
            self.test_output_dir = self.output_dir / time.strftime("%Y%m%d_%H%M%S")
            self.test_output_dir.mkdir(parents=True, exist_ok=True)

        # Initialize RapidOCR engine
        self.ocr_engine = RapidOcrEngine(
            rotations=rotations,
            logger=logger,
        )

        # Initialize tile group manager
        self.tile_manager = TileGroupManager(
            group_width=group_width,
            group_height=group_height,
            slide_step_x=slide_step_x,
            slide_step_y=slide_step_y,
            logger=logger,
        )

        # Initialize vision processor
        self.vision_processor = VisionProcessor(
            google_api_key=google_api_key,
            save_json_for_debugging=self.save_json_for_debugging,
            save_images_for_debugging=self.save_images_for_debugging,
            output_dir=self.test_output_dir,
            logger=logger,
        )

        # Initialize PMI processor
        self.pmi_processor = PmiProcessor(logger=logger)

        # Initialize PMI Agent processor
        agent_settings = PMIAnalysisSettings(
            api_key=openai_api_key, model_name="openai:gpt-4.1", temperature=0.0, timeout=15.0
        )
        self.agent_processor = PmiAgentProcessor(
            settings=agent_settings,
            save_images_for_debugging=self.save_images_for_debugging,
            save_json_for_debugging=self.save_json_for_debugging,
            output_dir=self.test_output_dir,
            logger=logger,
        )

        # Initialize Standards extractor
        self.standards_extractor = StandardsExtractor(
            logger=logger,
        )

        # Initialize Material extractor
        self.material_extractor = MaterialExtractor(
            settings=agent_settings,
            logger=logger,
        )

        # Initialize PostProcessResults
        self.post_processor = PostProcessResults(
            save_json_for_debugging=self.save_json_for_debugging,
            logger=logger,
            lf=self.lf,
            output_dir=self.test_output_dir,
        )

    async def process_drawing(
        self,
        bucket: str,
        tile_infos: List[TileInfo],
    ) -> DrawingAnalysisResult:
        """
        Process a drawing with OCR.

        Args:
            bucket: S3 bucket name
            tile_infos: List of dictionaries with tile information

        Returns:
            DrawingAnalysisResult containing PMI analysis results and metadata
        """
        start_time = time.time()
        self.logger.info(f"Processing drawing with {len(tile_infos)} tiles, rotations: {self.rotations}")

        # Step 1: Prepare tiles
        tiles, tile_size = self._prepare_tiles(bucket, tile_infos)

        # Step 2: Calculate canvas dimensions
        canvas_width, canvas_height, tiles_by_position, max_tile_x, max_tile_y = self._calculate_canvas_dimensions(
            tiles
        )
        self.logger.info(f"Canvas dimensions: {canvas_width}x{canvas_height}")

        # Step 3: Process tile groups with Rapid OCR
        # Run the potentially heavy OCR stitching in a worker thread so we don’t block the event loop
        loop = asyncio.get_running_loop()
        combined_result = await loop.run_in_executor(
            None,
            self._process_tile_groups,
            tiles,
            tile_size,
            canvas_width,
            canvas_height,
        )
        ocr_elapsed = time.time() - start_time

        if self.save_json_for_debugging and self.test_output_dir is not None:
            try:
                result_file = self.test_output_dir / "1_rapid_ocr_box_detections.json"
                with open(result_file, "w") as f:
                    result_data = OcrResultData(
                        processing_time=time.time() - start_time,
                        ocr_processing_time=ocr_elapsed,
                        num_tile_groups=len(self.tile_manager.group_tiles(tiles)),
                        num_tiles=len(tiles),
                        num_detections=len(combined_result),
                        results=combined_result.to_p1p2_dict(),
                        canvas_width=canvas_width,
                        canvas_height=canvas_height,
                    )
                    json.dump(result_data.__dict__, f, indent=2)
            except Exception as e:
                self.logger.error(f"Error saving OCR box detections: {str(e)}")

        # Step 4: Create background image
        background_image = self._create_background_image(
            tiles, canvas_height, canvas_width, tiles_by_position, max_tile_x, max_tile_y
        )

        # Step 5: Process OCR results
        pmi_results = await self._process_ocr_results(combined_result, background_image)

        total_elapsed = time.time() - start_time
        self.logger.info(f"Total processing completed in {total_elapsed:.2f}s")

        return pmi_results

    def _prepare_tiles(self, bucket: str, tile_infos: List[TileInfo]) -> Tuple[List[Tile], int]:
        """
        Fetch tiles and create Tile objects.

        Args:
            bucket: S3 bucket name
            tile_infos: List of dictionaries with tile information

        Returns:
            Tuple of (list of Tile objects, tile size)
        """
        # Fetch all tiles
        tile_data = self._fetch_tiles_in_parallel(bucket, tile_infos)

        if not tile_data:
            raise ValueError("Failed to fetch any tiles from S3")

        # Find a tile at position (0,0) for tile size determination
        # or fallback to the first tile if no (0,0) tile exists
        tile_size = None
        for tile_id, (data, x, y) in tile_data.items():
            if x == 0 and y == 0:
                # Found the top-left tile
                img = Image.open(io.BytesIO(data))
                tile_size = img.width
                break

        # If no top-left tile was found, use the first tile
        if tile_size is None:
            raise ValueError("Failed to determine tile size from tile_x0_y0")

        # Create Tile objects - get actual dimensions for each tile
        tiles: List[Tile] = []
        for tile_id, (data, x, y) in tile_data.items():
            try:
                # Get actual dimensions of the tile
                img = Image.open(io.BytesIO(data))
                actual_width, actual_height = img.size

                tile = self.tile_manager.create_tile_object(
                    tile_id=tile_id, tile_x=x, tile_y=y, width=actual_width, height=actual_height, image_data=data
                )
                tiles.append(tile)
            except Exception as e:
                self.logger.error(f"Failed to process tile {tile_id}: {str(e)}")
                raise ValueError(f"Failed to process tile {tile_id}")

        return tiles, tile_size

    def _process_tile_groups(
        self, tiles: List[Tile], tile_size: int, canvas_width: int, canvas_height: int
    ) -> OcrResult:
        """
        Process tile groups with OCR.

        Args:
            tiles: List of Tile objects
            tile_size: Size of each tile
            canvas_width: Width of the canvas
            canvas_height: Height of the canvas

        Returns:
            Combined OCR result
        """
        # Group tiles and process with OCR
        tile_groups = self.tile_manager.group_tiles(tiles)

        # Track all detections as we go - we'll simply collect without merging
        all_detections: List[Detection] = []

        # Process tile groups in parallel
        self.logger.info(f"Processing {len(tile_groups)} tile groups in parallel with {self.max_ocr_workers} workers")

        def process_single_tile_group(group_data: Tuple[int, Any]) -> List[Detection]:
            """Process a single tile group and return its detections."""
            i, group = group_data
            try:
                # Stitch tile group
                stitched_image = self.tile_manager.stitch_tile_group(group, tile_size)

                # Process with OCR engine
                group_result = self.ocr_engine.process_image_array(
                    stitched_image,
                    image_name=f"group_{i+1}_{group.offset_x}_{group.offset_y}",
                )

                # Transform coordinates to be relative to the full drawing
                transformed_result = self.tile_manager.transform_coordinates(
                    group_result,
                    group.offset_x,
                    group.offset_y,
                    tile_size,
                    canvas_width=canvas_width,
                    canvas_height=canvas_height,
                )

                # Return detections
                if transformed_result.detections:
                    return transformed_result.detections
                else:
                    self.logger.warning(f"No detections from tile group {i+1}")
                    return []
            except Exception as e:
                self.logger.error(f"Error processing tile group {i+1}: {str(e)}")
                return []

        # Process tile groups in parallel using ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=self.max_ocr_workers) as executor:
            # Submit all tile groups for processing
            futures = [executor.submit(process_single_tile_group, (i, group)) for i, group in enumerate(tile_groups)]

            # Collect results as they complete
            completed_count = 0
            for future in concurrent.futures.as_completed(futures):
                try:
                    detections = future.result()
                    all_detections.extend(detections)
                    completed_count += 1
                    self.logger.info(f"Completed tile group {completed_count}/{len(tile_groups)}")
                except Exception as e:
                    completed_count += 1
                    self.logger.error(f"Error getting result from tile group {completed_count}: {str(e)}")

        combined_result: OcrResult = OcrResult(detections=all_detections)
        return combined_result

    def _create_background_image(
        self,
        tiles: List[Tile],
        canvas_height: int,
        canvas_width: int,
        tiles_by_position: Dict[TilePosition, Tuple[int, int]],
        max_tile_x: int,
        max_tile_y: int,
    ) -> NDArray[np.uint8]:
        """
        Create background image from tiles.

        Args:
            tiles: List of Tile objects
            canvas_height: Height of the canvas
            canvas_width: Width of the canvas
            tiles_by_position: Dictionary mapping tile positions to dimensions
            max_tile_x: Maximum tile X coordinate
            max_tile_y: Maximum tile Y coordinate

        Returns:
            Background image as numpy array
        """
        # Create background image for visualization and enhancement using actual dimensions
        background_image: NDArray[np.uint8] = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)

        # Calculate tile positions based on actual dimensions
        x_positions: Dict[int, int] = {0: 0}
        y_positions: Dict[int, int] = {0: 0}

        # Calculate Y positions
        for y in range(1, max_tile_y + 1):
            pos_y: int = 0
            for y_prev in range(y):
                for x in range(max_tile_x + 1):
                    if (x, y_prev) in tiles_by_position:
                        pos_tile_dims = tiles_by_position[(x, y_prev)]
                        pos_tile_height = pos_tile_dims[1]
                        pos_y = max(pos_y, y_positions.get(y_prev, 0) + pos_tile_height)
            y_positions[y] = pos_y

        # Calculate X positions
        for x in range(1, max_tile_x + 1):
            pos_x: int = 0
            for x_prev in range(x):
                for y in range(max_tile_y + 1):
                    if (x_prev, y) in tiles_by_position:
                        pos_tile_dims = tiles_by_position[(x_prev, y)]
                        pos_tile_width = pos_tile_dims[0]
                        pos_x = max(pos_x, x_positions.get(x_prev, 0) + pos_tile_width)
            x_positions[x] = pos_x

        # Place tiles on the canvas
        for tile in tiles:
            try:
                # Convert tile image to numpy array
                img = Image.open(io.BytesIO(tile.image_data))
                img_array = np.array(img)

                # Convert to BGR if needed
                if len(img_array.shape) == 3 and img_array.shape[2] == 3:
                    img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
                elif len(img_array.shape) == 3 and img_array.shape[2] == 4:
                    img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2BGR)

                # Place the tile on the canvas using calculated positions
                x_pos = x_positions[tile.tile_x]
                y_pos = y_positions[tile.tile_y]
                img_shape: Tuple[int, ...] = img_array.shape
                img_height: int = int(img_shape[0])
                img_width: int = int(img_shape[1])

                # Ensure we don't exceed canvas boundaries
                if y_pos + img_height <= canvas_height and x_pos + img_width <= canvas_width:
                    background_image[y_pos : y_pos + img_height, x_pos : x_pos + img_width] = img_array
                else:
                    self.logger.warning(
                        f"Tile at ({tile.tile_x}, {tile.tile_y}) with size {img_width}x{img_height} "
                        f"exceeds canvas size at position ({x_pos}, {y_pos})"
                    )
            except Exception as e:
                self.logger.error(f"Error placing tile on canvas: {str(e)}")

        return background_image

    async def _process_ocr_results(
        self, combined_result: OcrResult, background_image: NDArray[np.uint8]
    ) -> DrawingAnalysisResult:
        """
        Process OCR results with Google Vision and PMI processor.

        Args:
            combined_result: Combined OCR result
            background_image: Background image as numpy array

        Returns:
            DrawingAnalysisResult containing PMI analysis results and metadata
        """
        # Initialize an empty result
        final_ocr_results = DrawingAnalysisResult(results={}, metadata=None)
        # Process OCR results with Google Vision API
        document_text = DocumentText(text="")  # Initialize with empty text

        classified_doc = None  # Initialize classified document
        if len(combined_result) > 0:
            try:
                # Create a mask for text regions
                h, w = background_image.shape[:2]
                mask = np.zeros((h, w), dtype=np.uint8)

                # Draw white rectangles on the mask for each text region
                for detection in combined_result.detections:
                    try:
                        # Get box coordinates
                        box_coords = detection[0]

                        # Convert normalized coordinates to absolute values
                        absolute_box_coords = []
                        for point in box_coords:
                            if len(point) >= 2:
                                # Convert normalized coordinates back to absolute values
                                abs_x = int(float(point[0]) * w)
                                abs_y = int(float(point[1]) * h)
                                absolute_box_coords.append([abs_x, abs_y])

                        # Convert to numpy array
                        pts = np.array(absolute_box_coords, dtype=np.int32)

                        # Draw filled white polygon on the mask
                        cv2.fillPoly(mask, [pts], (255, 255, 255))
                    except Exception as e:
                        self.logger.error(f"Error processing detection for mask: {str(e)}")

                # Apply the mask to the background image
                masked_image = np.zeros_like(background_image)
                for c in range(3):  # Apply to each color channel
                    masked_image[:, :, c] = cv2.bitwise_and(background_image[:, :, c], mask)

                # Save the masked image for debugging
                if self.save_images_for_debugging and self.test_output_dir is not None:
                    try:
                        masked_image_path = self.test_output_dir / "2_masked_image_for_ocr.png"
                        cv2.imwrite(str(masked_image_path), masked_image)
                    except Exception as e:
                        self.logger.error(f"Error saving masked image: {str(e)}")

                # Use the masked image for Google Vision API
                _, document_text = await self.vision_processor.process_ocr_result(
                    image=masked_image,  # Use masked image instead of full background image
                    ocr_result=combined_result,
                )

                # Process with PMI processor
                if document_text.text_blocks:
                    classified_doc = self.pmi_processor.process_document_text(document_text)

                    # Save classified document to JSON
                    if self.save_json_for_debugging and self.test_output_dir is not None:
                        try:
                            self.pmi_processor.save_classified_document(
                                classified_doc=classified_doc,
                                output_dir=self.test_output_dir,
                            )
                        except Exception as e:
                            self.logger.error(f"Error saving classified document: {str(e)}")

                    # Create PMI visualizations
                    if self.save_images_for_debugging and self.test_output_dir is not None:
                        try:
                            self.pmi_processor.visualize_pmi_classifications(
                                image=background_image,
                                classified_doc=classified_doc,
                                output_dir=self.test_output_dir,
                            )
                        except Exception as e:
                            self.logger.error(f"Error creating PMI visualizations: {str(e)}")

                    # Process PMI blocks with the initialized agent processor
                    try:
                        # Process PMI blocks using the agent processor instance
                        raw_results = await self.agent_processor.process_blocks(
                            classified_doc=classified_doc,
                            image=background_image,
                        )

                        # Process standards blocks to extract ISO standards information
                        extracted_standards = self.standards_extractor.extract_standards(classified_doc=classified_doc)

                        if extracted_standards:
                            self.logger.info(f"Extracted {len(extracted_standards)} standards from the drawing")
                        else:
                            self.logger.info("No standards extracted from the drawing")

                        # Extract material information from the drawing
                        extracted_materials = await self.material_extractor.extract_materials(
                            classified_doc=classified_doc
                        )

                        # Post-process the results using the post processor
                        normalized_results = self.post_processor.post_process_pmi_results(
                            results=raw_results,
                            image=background_image,
                            extracted_standards=extracted_standards,
                        )

                        # Create metadata object
                        metadata = None
                        if extracted_standards or extracted_materials:
                            metadata = PMIMetadata(
                                general_tolerance_info=extracted_standards[0] if extracted_standards else None,
                                material_info=extracted_materials[0] if extracted_materials else None,
                            )
                            self.logger.info(f"Created metadata: {metadata}")

                        # Create the final result object
                        final_ocr_results = DrawingAnalysisResult(
                            results=normalized_results,
                            metadata=metadata,
                        )

                        # Save final results to JSON if debugging is enabled
                        if self.save_json_for_debugging:
                            self._save_final_results_to_json(final_ocr_results)

                        if self.save_images_for_debugging:
                            visualize_pmi_results(
                                background_image, final_ocr_results.results, self.test_output_dir, self.logger
                            )

                    except Exception as e:
                        self.logger.error(f"Error during agent-based PMI analysis: {str(e)}")
                        self.logger.error(traceback.format_exc())

                # Create visualizations if requested
                if self.save_images_for_debugging:
                    self._create_visualizations(background_image, combined_result, document_text)

            except Exception as e:
                self.logger.error(f"Error during Google Vision processing: {str(e)}")
                document_text = DocumentText(text="")
                classified_doc = None

        return final_ocr_results

    def _create_visualizations(
        self,
        background_image: NDArray[np.uint8],
        combined_result: OcrResult,
        document_text: DocumentText,
    ) -> None:
        """
        Create visualizations for debugging.

        Args:
            background_image: Background image as numpy array
            combined_result: Combined OCR result
            document_text: Document text from Google Vision
        """
        if self.test_output_dir is None:
            self.logger.warning("Cannot create visualizations: test_output_dir is None")
            return

        try:
            rapid_ocr_box_detections_path = self.test_output_dir / "1_rapid_ocr_box_detections.png"
            visualize_text_regions(
                image=background_image,
                ocr_result=combined_result,
                output_path=rapid_ocr_box_detections_path,
            )

            # Google Vision visualizations for text blocks and elements
            # 2_google_vision_blocks.png
            # 2_google_vision_elements.png
            visualize_pre_processed_results(
                image=background_image,
                document_text=document_text,
                output_dir=self.test_output_dir,
                logger=self.logger,
            )
        except Exception as e:
            self.logger.error(f"Error creating visualizations: {str(e)}")

    def _fetch_tiles_in_parallel(
        self, bucket: str, tile_infos: List[TileInfo], timeout: int = 30
    ) -> Dict[str, TileData]:
        """
        Fetch multiple tiles from S3 in parallel.

        Args:
            bucket: S3 bucket name
            tile_infos: List of dictionaries with tile information
            timeout: Timeout in seconds for the entire operation

        Returns:
            Dictionary mapping tile_id to (image_data, x, y, width, height)
        """
        start_time = time.time()

        result: Dict[str, TileData] = {}

        def fetch_tile(tile_info: TileInfo) -> Tuple[str, Optional[TileData]]:
            """Fetch a single tile and return its ID and data."""
            tile_id = str(tile_info.tile_obj_id)
            tile_x = int(tile_info.tile_x)
            tile_y = int(tile_info.tile_y)

            try:
                # Get object from S3
                obj = self.s3_storage.get_object(bucket, PurePosixPath(tile_id))
                data = obj.read()
                return tile_id, (data, tile_x, tile_y)
            except Exception as e:
                self.logger.error(f"Error fetching tile {tile_id}: {str(e)}")
                return tile_id, None

        # Use ThreadPoolExecutor for parallel fetching
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [executor.submit(fetch_tile, tile_info) for tile_info in tile_infos]

            # Wait for results with timeout
            remaining_time = timeout
            for future in futures:
                try:
                    tile_id, tile_data = future.result(timeout=remaining_time)
                    if tile_data is not None:
                        result[tile_id] = tile_data
                except Exception as e:
                    self.logger.error(f"Error in tile fetching: {str(e)}")

                # Update remaining time
                remaining_time = int(max(0, timeout - (time.time() - start_time)))
                if remaining_time <= 0:
                    self.logger.warning("Tile fetching timeout reached")
                    break

        elapsed = time.time() - start_time
        self.logger.info(f"Fetched {len(result)}/{len(tile_infos)} tiles in {elapsed:.2f}s")

        return result

    def _save_final_results_to_json(self, final_results: DrawingAnalysisResult) -> None:
        """
        Save final results to a JSON file.

        Args:
            final_results: DrawingAnalysisResult containing PMI analysis results and metadata
        """
        try:
            if self.test_output_dir is None:
                self.logger.warning("Cannot save final results: test_output_dir is None")
                return

            # Create a timestamp for the filename
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            result_file = self.test_output_dir / f"5_final_results_{timestamp}.json"

            # Convert results to serializable format
            serializable_results: Dict[str, Any] = {}

            # Convert PMI results
            serializable_results["results"] = {}
            for classification, blocks in final_results.results.items():
                serializable_results["results"][classification] = []
                for block in blocks:
                    # Create a serializable representation of the block
                    block_dict: Dict[str, Any] = {
                        # Include all fields from the result except bounding_poly
                        "result": {k: v for k, v in block.result.model_dump().items() if k != "bounding_poly"},
                        # Include the normalized polygon
                        "polygon": block.polygon.model_dump(),
                        # Include the tolerance source if available
                        "tolerance_source": block.tolerance_source,
                    }
                    serializable_results["results"][classification].append(block_dict)

            # Add metadata if available
            if final_results.metadata:
                serializable_results["metadata"] = final_results.metadata.model_dump()

            # Write to JSON file
            with open(result_file, "w") as f:
                json.dump(serializable_results, f, indent=2)

            self.logger.info(f"Saved final results to {result_file}")
        except Exception as e:
            self.logger.error(f"Error saving final results to JSON: {str(e)}")

    def _calculate_canvas_dimensions(
        self, tiles: List[Tile]
    ) -> Tuple[int, int, Dict[TilePosition, Tuple[int, int]], int, int]:
        """
        Calculate canvas dimensions based on tile sizes and positions.

        Args:
            tiles: List of tiles

        Returns:
            Tuple of (canvas_width, canvas_height, tiles_by_position, max_tile_x, max_tile_y)
        """
        # Map tiles by position
        tiles_by_position: Dict[TilePosition, Tuple[int, int]] = {}
        for tile in tiles:
            tiles_by_position[(tile.tile_x, tile.tile_y)] = (tile.width, tile.height)

        # Calculate canvas dimensions using actual tile sizes
        max_tile_x: int = max(tile.tile_x for tile in tiles)
        max_tile_y: int = max(tile.tile_y for tile in tiles)

        # Calculate column offsets
        col_offsets: List[int] = [0] * (max_tile_x + 2)  # +2 to avoid index errors
        for x in range(max_tile_x + 1):
            for y in range(max_tile_y + 1):
                if (x, y) in tiles_by_position:
                    col_tile_dims = tiles_by_position[(x, y)]
                    col_tile_width = col_tile_dims[0]
                    col_offsets[x + 1] = max(col_offsets[x + 1], col_offsets[x] + col_tile_width)

        # Calculate row offsets
        row_offsets: List[int] = [0] * (max_tile_y + 2)  # +2 to avoid index errors
        for y in range(max_tile_y + 1):
            for x in range(max_tile_x + 1):
                if (x, y) in tiles_by_position:
                    row_tile_dims = tiles_by_position[(x, y)]
                    row_tile_height = row_tile_dims[1]
                    row_offsets[y + 1] = max(row_offsets[y + 1], row_offsets[y] + row_tile_height)

        # Final canvas dimensions
        canvas_width: int = col_offsets[max_tile_x + 1]
        canvas_height: int = row_offsets[max_tile_y + 1]

        return canvas_width, canvas_height, tiles_by_position, max_tile_x, max_tile_y
