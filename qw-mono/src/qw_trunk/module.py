from typing import Optional

from qw_agent_service.agent.agent_service import AgentService
from qw_agent_service.agent.service_provider import ServiceProvider
from qw_basic_rdb.interface import RelationalDatabase
from qw_basic_s3.interface import S3Storage
from qw_basic_s3.schema import S3BucketDefinition, S3StorageSchema
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_monodb.table.base import Base
from qw_tenant_config.registry import TenantConfigRegistry
from qw_trunk.config import QwTrunkApiKeys, QwTrunkConfig
from qw_trunk.service.chat_db import ChatDatabaseService, ChatDatabaseServiceSettings
from qw_trunk.service.drawing.db import DrawingDatabaseService
from qw_trunk.service.drawing.discussion import DrawingDiscussionService
from qw_trunk.service.drawing.drawing import DrawingService, DrawingServiceSettings
from qw_trunk.service.drawing.technical_drawing_analysis_client import TechnicalDrawingAnalysisClient
from qw_trunk.service.drawing.tile import DrawingTileService
from qw_trunk.service.healthcheck import HealthCheckService
from qw_trunk.service.inspection.acl import InspectionAclService
from qw_trunk.service.inspection.db import InspectionDatabaseService
from qw_trunk.service.inspection.pdfgen import InspectionResultPDFGenerationService
from qw_trunk.service.inspection.plan import InspectionPlanService
from qw_trunk.service.inspection.result import InspectionResultService
from qw_trunk.service.material.db import MaterialDatabaseService
from qw_trunk.service.material.material import MaterialService
from qw_trunk.service.material_certificate.db import MaterialCertificateDatabaseService
from qw_trunk.service.material_certificate.material_certificate import MaterialCertificateService
from qw_trunk.service.order.db import OrderDatabaseService
from qw_trunk.service.order.importer import OrderLineImportService
from qw_trunk.service.order.order import OrderService
from qw_trunk.service.order.task import OrderLineTaskService
from qw_trunk.service.resource.file import FileResourceService
from qw_trunk.service.resource.file_acl import FileResourceAclService
from qw_trunk.service.resource.file_db import FileResourceDatabaseService
from qw_trunk.service.resource.file_link import FileResourceLinkService
from qw_trunk.service.resource.s3_layout import S3BucketLayout
from qw_trunk.service.resource.s3_object import S3ObjectService
from qw_trunk.service.search import SearchService
from qw_trunk.service.tenant.db import TenantDatabaseService
from qw_trunk.service.user import UserService
from qw_trunk.service.user_db import UserDatabaseService
from qw_trunk.service.wopi import WopiService, WopiServiceSettings


class QwTrunkModule(object):
    def __init__(
        self,
        agent_service: AgentService,
        user_service: UserService,
        file_service: FileResourceService,
        drawing_service: DrawingService,
        drawing_analysis_client: TechnicalDrawingAnalysisClient,
        drawing_discussion_service: DrawingDiscussionService,
        material_service: MaterialService,
        material_certificate_service: MaterialCertificateService,
        order_service: OrderService,
        order_line_import_service: OrderLineImportService,
        order_line_task_service: OrderLineTaskService,
        inspection_plan_service: InspectionPlanService,
        inspection_result_service: InspectionResultService,
        inspection_result_pdf_generation_service: InspectionResultPDFGenerationService,
        chat_db_service: ChatDatabaseService,
        search_service: SearchService,
        wopi_service: WopiService,
        health_check_service: HealthCheckService,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        self.agent_service = agent_service
        self.user_service = user_service
        self.file_service = file_service
        self.drawing_service = drawing_service
        self.drawing_analysis_client = drawing_analysis_client
        self.drawing_discussion_service = drawing_discussion_service
        self.material_certificate_service = material_certificate_service
        self.material_service = material_service
        self.order_service = order_service
        self.order_line_import_service = order_line_import_service
        self.order_line_task_service = order_line_task_service
        self.inspection_plan_service = inspection_plan_service
        self.inspection_result_service = inspection_result_service
        self.inspection_result_pdf_generation_service = inspection_result_pdf_generation_service
        self.chat_db_service = chat_db_service
        self.search_service = search_service
        self.wopi_service = wopi_service
        self.health_check_service = health_check_service
        self.lf = lf

    @classmethod
    def build(
        cls,
        rdb: RelationalDatabase,
        s3: S3Storage,
        tenant_registry: TenantConfigRegistry,
        drawing_settings: DrawingServiceSettings,
        chat_db_settings: ChatDatabaseServiceSettings | None,
        wopi_settings: WopiServiceSettings,
        api_keys: QwTrunkApiKeys,
        config_path: Optional[str] = None,
        lf: LogFactory = NO_LOG_FACTORY,
    ) -> "QwTrunkModule":
        s3_layout = S3BucketLayout()
        s3_service = S3ObjectService(rdb, s3, lf=lf)

        tenant_db_service = TenantDatabaseService(rdb, lf=lf)

        user_db_service = UserDatabaseService(rdb, lf=lf)

        drawing_db_service = DrawingDatabaseService(rdb, lf=lf)
        drawing_service = DrawingService(
            db_service=drawing_db_service,
            tile_service=DrawingTileService(
                rdb, s3_service, tenant_registry, drawing_db_service=drawing_db_service, config_path=config_path, lf=lf
            ),
            s3_service=s3_service,
            settings=drawing_settings,
            lf=lf,
        )

        drawing_analysis_client = TechnicalDrawingAnalysisClient(rdb=rdb, lf=lf)

        chat_db_service = ChatDatabaseService(rdb, chat_db_settings, lf)

        material_db_service = MaterialDatabaseService(rdb, lf=lf)
        material_service = MaterialService(
            chat_db_service=chat_db_service, db_service=material_db_service, tenant_registry=tenant_registry, lf=lf
        )

        material_cert_db_service = MaterialCertificateDatabaseService(rdb, lf=lf)
        material_cert_service = MaterialCertificateService(
            db_service=material_cert_db_service,
            s3_service=s3_service,
            lf=lf,
        )

        order_db_service = OrderDatabaseService(rdb, lf=lf)
        order_service = OrderService(
            order_db_service=order_db_service,
            chat_db_service=chat_db_service,
            material_db_service=material_db_service,
            lf=lf,
        )

        pol_import_service = OrderLineImportService(
            order_service=order_service,
            material_service=material_service,
            tenant_registry=tenant_registry,
            lf=lf,
        )

        file_db_service = FileResourceDatabaseService(rdb, lf=lf)
        file_acl_service = FileResourceAclService(rdb, file_db_service, lf=lf)
        file_link_service = FileResourceLinkService(
            rdb=rdb,
            file_db=file_db_service,
            file_acl=file_acl_service,
            order_db=order_db_service,
            material_db=material_db_service,
            tenant_db=tenant_db_service,
            lf=lf,
        )
        file_service = FileResourceService(
            rdb=rdb,
            s3_service=s3_service,
            s3_layout=s3_layout,
            file_acl=file_acl_service,
            file_db=file_db_service,
            file_link=file_link_service,
            hook_generators=[drawing_service, material_cert_service],
            tenant_registry=tenant_registry,
            lf=lf,
        )
        drawing_discussion_service = DrawingDiscussionService(
            drawing_db_service=drawing_db_service,
            chat_db_service=chat_db_service,
            file_db_service=file_db_service,
            lf=lf,
        )

        inspection_db_service = InspectionDatabaseService(rdb, lf=lf)
        inspection_acl_service = InspectionAclService(rdb, lf=lf)
        inspection_plan_service = InspectionPlanService(
            inspection_db_service=inspection_db_service,
            inspection_acl_service=inspection_acl_service,
            drawing_db_service=drawing_db_service,
            order_db_service=order_db_service,
            tenant_registry=tenant_registry,
            lf=lf,
        )
        inspection_result_service = InspectionResultService(
            inspection_db_service=inspection_db_service,
            inspection_acl_service=inspection_acl_service,
            inspection_plan_service=inspection_plan_service,
            s3_obj_service=s3_service,
            s3_layout=s3_layout,
            tenant_registry=tenant_registry,
            lf=lf,
        )

        order_line_task_service = OrderLineTaskService(
            order_db_service=order_db_service,
            # TODO: Once order line tasks are phased out this cross dependency shall be removed
            inspection_result_service=inspection_result_service,
            inspection_acl_service=inspection_acl_service,
            lf=lf,
        )

        inspection_result_pdf_generation_service = InspectionResultPDFGenerationService(
            object_service=s3_service,
            inspection_db_service=inspection_db_service,
            order_line_task_service=order_line_task_service,
            material_service=material_service,
            file_service=file_service,
            lf=lf,
        )

        search_service = SearchService(
            order_db=order_db_service,
            material_db=material_db_service,
            file_db=file_db_service,
            tenant_registry=tenant_registry,
            lf=lf,
        )

        wopi_service = WopiService(wopi_settings, lf=lf)
        health_check_service = HealthCheckService(rdb, s3, wopi_service, lf=lf)

        service_provider = ServiceProvider(
            drawing_analysis_client=drawing_analysis_client,
            file_service=file_service,
            s3_service=s3_service,
            material_certificate_service=material_cert_service,
            order_service=order_service,
            inspection_plan_service=inspection_plan_service,
            material_service=material_service,
            drawing_service=drawing_service,
            lf=lf,
        )

        # Now that all required services are created, initialize the agent service
        agent_service = AgentService(
            api_key=api_keys.openai_key,
            service_provider=service_provider,
            lf=lf,
        )

        return cls(
            agent_service=agent_service,
            user_service=UserService(user_db_service, tenant_registry, lf),
            file_service=file_service,
            drawing_service=drawing_service,
            drawing_analysis_client=drawing_analysis_client,
            drawing_discussion_service=drawing_discussion_service,
            material_service=material_service,
            material_certificate_service=material_cert_service,
            order_service=order_service,
            order_line_import_service=pol_import_service,
            order_line_task_service=order_line_task_service,
            inspection_plan_service=inspection_plan_service,
            inspection_result_service=inspection_result_service,
            inspection_result_pdf_generation_service=inspection_result_pdf_generation_service,
            search_service=search_service,
            chat_db_service=chat_db_service,
            wopi_service=wopi_service,
            health_check_service=health_check_service,
            lf=lf,
        )

    @classmethod
    def from_config(cls, cfg: QwTrunkConfig, tenant_registry: TenantConfigRegistry, lf: LogFactory) -> "QwTrunkModule":
        logger = lf.get_logger(__name__)

        storage_schema = S3StorageSchema(
            buckets=[S3BucketDefinition(name=b) for b in tenant_registry.get_tenant_buckets()]
        )

        rdb = cfg.db.build(Base, logger=logger)
        s3 = cfg.s3.build(storage_schema, lf=lf)

        # Get the config path - we need to find a way to get this
        import sys

        config_path: Optional[str] = None
        for i, arg in enumerate(sys.argv):
            if arg == "--qw-mono-config" and i + 1 < len(sys.argv):
                config_path = sys.argv[i + 1]
                break

        return cls.build(
            rdb=rdb,
            s3=s3,
            tenant_registry=tenant_registry,
            drawing_settings=cfg.drawing_settings,
            chat_db_settings=cfg.chat_db_settings,
            wopi_settings=cfg.wopi_settings,
            api_keys=cfg.api_keys,
            config_path=config_path,
            lf=lf,
        )
