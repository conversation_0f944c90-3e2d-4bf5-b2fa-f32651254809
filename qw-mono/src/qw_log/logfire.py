"""
Logfire integration for qw_log.

This module provides integration with the logfire observability platform.
It handles initialization and configuration of logfire for observability.

Logfire is NOT a general-purpose logging library. It is specifically designed
for observability and tracing. Use it directly in components that need observability.
"""
import logging
from typing import Optional

import logfire
from pydantic_ai import Agent


class LogfireIntegration:
    """
    Integration with logfire observability platform.

    This class provides methods to initialize and configure logfire
    for observability across the application.
    """

    _initialized: bool = False
    _logger = logging.getLogger(__name__)

    @classmethod
    def initialize(cls, token: Optional[str] = None, environment: Optional[str] = None, instrument_pydantic_ai: bool = True) -> bool:
        """
        Initialize logfire with the provided token.

        Args:
            token: The logfire API token
            instrument_pydantic_ai: Whether to instrument pydantic-ai with logfire

        Returns:
            True if initialization was successful, False otherwise
        """
        if cls._initialized:
            cls._logger.info("Logfire already initialized")
            return True

        try:
            # Configure logfire with the provided token and environment
            if token:
                cls._logger.info("Initializing logfire")
                logfire.configure(token=token, environment=environment)

                # Instrument pydantic-ai if requested
                if instrument_pydantic_ai:
                    try:
                        cls._logger.info(f"Instrumenting pydantic-ai with logfire with environment {environment} and token {token[:10]}...")
                        # Instrument all agents to use logfire
                        Agent.instrument_all()

                        # Instrument HTTPX for API call monitoring
                        cls._logger.info("Instrumenting HTTPX with logfire")
                        logfire.instrument_httpx(capture_all=True)
                    except ImportError:
                        cls._logger.warning("pydantic-ai not available, skipping instrumentation")
                        pass

                cls._initialized = True
                cls._logger.info("Logfire initialized successfully")
                return True
            else:
                cls._logger.warning("No logfire token provided, skipping initialization")
                return False
        except Exception as e:
            cls._logger.error(f"Failed to initialize logfire: {str(e)}")
            return False

    @classmethod
    def is_initialized(cls) -> bool:
        """Check if logfire has been initialized."""
        return cls._initialized
