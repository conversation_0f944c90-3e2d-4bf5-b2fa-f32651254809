import logging
from typing import Literal, Optional

from pydantic import BaseModel

from qw_log.logfire import LogfireIntegration
from qw_log.logger import QwLogger


class QwLogfireConfig(BaseModel):
    """Configuration for logfire observability platform."""

    enabled: bool = False
    token: Optional[str] = None
    instrument_pydantic_ai: bool = True
    environment: Optional[str] = None


class QwLogConfig(BaseModel):
    level: Literal["INFO", "DEBUG"] = "INFO"
    fmt: str = "%(asctime)s.%(msecs)03d | %(levelname)-7s | %(name)-40.40s   %(message)s"
    fmt_date: str = "%Y-%m-%d %H:%M:%S"
    logfire: Optional[QwLogfireConfig] = None

    @property
    def log_level(self) -> int:
        if self.level == "DEBUG":
            return logging.DEBUG
        return logging.INFO


class QwLogFactory(object):
    """
    Class with compatible interface to ``qw_log_interface.LogFactory``.
    Motivation is to have uniform logging across services and enforce certain metadata (e.g. service version).
    """

    def __init__(self, version: str, name: str | None = None):
        self.version = version
        self.__name: str | None = name

    def get_logger(self, name: str) -> QwLogger:
        # TODO: implement own logging creation
        logger_name = name if self.__name is None else self.__name
        return QwLogger(logging.getLogger(logger_name))

    def named_copy(self, name: str) -> "QwLogFactory":
        return QwLogFactory(self.version, name)

    @classmethod
    def init_logs(cls, cfg: QwLogConfig | None = None) -> None:
        """
        Initialize logging and logfire if configured.

        Args:
            cfg: Configuration for logging and logfire
        """
        if cfg is None:
            cfg = QwLogConfig()

        # Initialize standard logging
        logging.basicConfig(
            level=cfg.log_level,
            format=cfg.fmt,
            datefmt=cfg.fmt_date,
        )

        # Initialize logfire if configured
        if cfg.logfire and cfg.logfire.enabled and cfg.logfire.token:
            LogfireIntegration.initialize(
                token=cfg.logfire.token, instrument_pydantic_ai=cfg.logfire.instrument_pydantic_ai,
                environment=cfg.logfire.environment,
            )
