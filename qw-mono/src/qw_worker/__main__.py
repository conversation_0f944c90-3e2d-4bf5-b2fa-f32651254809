"""Main entry point for the Celery worker process."""
import argparse
from pathlib import Path

from qw_config.loader import load_config
from qw_log.factory import QwLogFactory
from qw_worker.config import QwWorkerMonoConfig
from qw_worker.logging import CeleryLogManager
from qw_worker.module import QwWorkerModule


def main() -> None:
    """Main entry point for the worker process."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Celery worker for background task processing")
    parser.add_argument("--qw-mono-config", type=Path, required=True)
    parser.add_argument("--qw-mono-overwrite-config", type=Path, default=None)
    args = parser.parse_args()

    # Load the worker configuration including logging settings
    worker_mono_cfg = load_config(QwWorkerMonoConfig, args.qw_mono_config, args.qw_mono_overwrite_config)

    # Initialize log factory with proper configuration
    lf = QwLogFactory("0.1.0", "qw_worker")
    lf.init_logs(worker_mono_cfg.logging)
    logger = lf.get_logger(__name__)
    logger.info("Worker process starting with logfire configuration")

    # Create Celery log manager (no need to pass logging config as it's already initialized)
    CeleryLogManager(lf)

    # Initialize worker module
    logger.info(f"Loading configuration from {args.qw_mono_config}")
    worker_module = QwWorkerModule.from_config(args.qw_mono_config, args.qw_mono_overwrite_config, lf)

    # Get the Celery app
    app = worker_module.get_celery_app()

    # Start the Celery worker
    logger.info("Starting Celery worker")

    # Start the worker using the Worker class directly
    # This is the recommended approach for Celery 5.x
    worker = app.Worker(
        loglevel="INFO",
        concurrency=None,  # Use the value from config
        # Enable task events for better monitoring
        task_events=True,
        # Redirect stdout/stderr to logger
        redirect_stdouts=True,
        # Log level for redirected stdout
        redirect_stdouts_level="INFO",
    )
    worker.start()


if __name__ == "__main__":
    main()
