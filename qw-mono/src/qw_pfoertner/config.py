from pydantic import BaseModel

from qw_basic_iam_policy.policy import QwPolicySettings
from qw_basic_keycloak.impl.keycloak.iam import KeycloakIamConfig
from qw_basic_rdb.builder.base import RelationalDatabaseConfig
from qw_pfoertner.service.session import SessionServiceSettings
from qw_pfoertner.service.wopi_auth import WopiAuthenticationServiceSettings


class QwPfoertnerSettings(BaseModel):
    include_spec_route: bool = False
    max_body_part_buffer_size: int | None = 1000 * 1000 * 50  # 50mb


class QwPfoertnerBaseConfig(BaseModel):
    tkn_gateway_salt: int
    iam: KeycloakIamConfig
    session_settings: SessionServiceSettings
    wopi_auth_settings: WopiAuthenticationServiceSettings
    policy_settings: QwPolicySettings
    settings: QwPfoertnerSettings


class QwPfoertnerConfig(QwPfoertnerBaseConfig):
    db: RelationalDatabaseConfig
