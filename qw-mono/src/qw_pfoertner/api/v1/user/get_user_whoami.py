import falcon

from qw_basic_iam_policy.common import AllowAllSessions
from qw_falcon_openapi.controller import OpenApiPathController
from qw_falcon_openapi.model import OpenApiBaseModel
from qw_falcon_openapi.operation import NoParams, OpenApiOpOutput, OpenApiOpParams
from qw_log_interface import NO_LOG_FACTORY, LogFactory, Logger
from qw_pfoertner.api.auth import AuthContext, AuthOpenApiOpJsonOut
from qw_pfoertner.service.session import SessionService
from qw_tenant_config.registry import TenantConfigRegistry
from qw_trunk.service.user_db import UserDatabaseService


class WhoamiView(OpenApiBaseModel):
    id: int
    tenant_id: int | None
    given_name: str
    family_name: str
    email: str


class GetUserWhoami(AuthOpenApiOpJsonOut[WhoamiView, NoParams, NoParams, NoParams]):
    def __init__(
        self,
        session_service: SessionService,
        user_db_service: UserDatabaseService,
        tenant_registry: TenantConfigRegistry,
        logger: Logger,
    ):
        super().__init__(WhoamiView, NoParams, NoParams, NoParams, session_service, logger)
        self.user_db_service = user_db_service
        self.tenant_registry = tenant_registry

    def on_request_after_auth(
        self, auth_ctx: AuthContext, params: OpenApiOpParams[NoParams, NoParams, NoParams]
    ) -> OpenApiOpOutput[WhoamiView]:
        auth_ctx.eval_policy(AllowAllSessions())
        user = self.user_db_service.find_user_by_issuer_and_subject(auth_ctx.tkn.issuer, auth_ctx.tkn.subject)
        if user is None:
            raise falcon.HTTPBadRequest()
        return OpenApiOpOutput(
            WhoamiView(
                id=user.id,
                tenant_id=user.tenant_id,
                given_name=user.given_name,
                family_name=user.family_name,
                email=user.email,
            )
        )


class GetUserWhoamiController(OpenApiPathController):
    def __init__(
        self,
        session_service: SessionService,
        user_db_service: UserDatabaseService,
        tenant_registry: TenantConfigRegistry,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        logger = lf.get_logger(__name__)
        super().__init__(
            route="/api/v1/user/whoami",
            get=GetUserWhoami(session_service, user_db_service, tenant_registry, logger),
            logger=logger,
        )
