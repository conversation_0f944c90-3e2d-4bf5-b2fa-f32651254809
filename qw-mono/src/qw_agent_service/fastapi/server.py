"""FastAPI Agent Service Server."""
from pathlib import Path
from typing import Optional

import uvicorn

from qw_agent_service.config import AgentServiceConfig
from qw_agent_service.fastapi.app import app
from qw_agent_service.fastapi.authentication.authentication_service import initialize_auth_service
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class FastApiApp:
    """FastAPI Agent Service Application."""

    def __init__(
        self,
        config: AgentServiceConfig,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the agent service application.
        """
        self.config = config
        self.lf = lf
        self.logger = lf.get_logger(__name__)

    def run(self) -> None:
        """
        Run the FastAPI agent service.
        """
        # Initialize authentication service with config
        initialize_auth_service(self.config, self.lf)

        # Store config in app state for access by endpoints
        app.state.config = self.config
        app.state.log_factory = self.lf

        # Configure uvicorn

        self.logger.info("Starting uvicorn server with FastAPI agent service")
        uvicorn.run(
            app=app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            reload=False,
            access_log=True,
        )

    @classmethod
    def from_config(
        cls,
        config_path: Path,
        config_overwrite_path: Optional[Path] = None,
        lf: LogFactory = NO_LOG_FACTORY,
    ) -> "FastApiApp":
        """
        Create agent service app from configuration files.
        """
        logger = lf.get_logger(__name__)
        logger.info(f"Initializing agent service from config: {config_path}")

        # Load configuration
        config = AgentServiceConfig.from_config(config_path, config_overwrite_path)

        return cls(config=config, lf=lf)
