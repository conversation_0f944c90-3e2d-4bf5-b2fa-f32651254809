"""Authentication middleware for FastAPI Agent Service."""
from typing import <PERSON><PERSON>
from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>, Request, Depends

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.fastapi.authentication.session_validator import <PERSON>Validator
from qw_agent_service.fastapi.authentication.models import AuthContext
from qw_agent_service.config import AgentServiceConfig


class AuthenticationService:
    """Service for handling authentication in FastAPI agent service."""

    def __init__(self, config: AgentServiceConfig, lf: LogFactory = NO_LOG_FACTORY):
        """
        Initialize authentication service.
        """
        self.config = config
        self.logger = lf.get_logger(__name__)
        self.session_validator = SessionValidator.from_config(config.auth_config, lf)

    async def validate_request(self, request: Request) -> AuthContext:
        """
        Validate incoming request and return authentication context.
        """
        # Extract session cookie
        session_token = request.cookies.get("session")
        if not session_token:
            self.logger.warning("Request missing session cookie")
            raise HTTPException(status_code=401, detail="Session cookie required")

        # Validate session
        auth_context = await self.session_validator.validate_session(session_token)
        if not auth_context:
            self.logger.warning(f"Session validation failed for token: {session_token[:10]}...")
            raise HTTPException(status_code=401, detail="Invalid or expired session")

        self.logger.info(f"Request authenticated for subject: {auth_context.subject}")
        return auth_context

    @classmethod
    def from_config(cls, config: AgentServiceConfig, lf: LogFactory = NO_LOG_FACTORY) -> "AuthenticationService":
        """
        Create AuthenticationService from configuration.
        """
        return cls(config=config, lf=lf)


# Global authentication service instance
_auth_service: Optional[AuthenticationService] = None


def get_auth_service() -> AuthenticationService:
    """
    Get the global authentication service instance.
    """
    global _auth_service
    if _auth_service is None:
        raise RuntimeError("Authentication service not initialized")
    return _auth_service


def initialize_auth_service(config: AgentServiceConfig, lf: LogFactory = NO_LOG_FACTORY) -> None:
    """
    Initialize the global authentication service.
    """
    global _auth_service
    _auth_service = AuthenticationService.from_config(config, lf)


async def get_current_user(
    request: Request,
    auth_service: AuthenticationService = Depends(get_auth_service)
) -> AuthContext:
    """
    FastAPI dependency to get current authenticated user.
    """
    return await auth_service.validate_request(request)
