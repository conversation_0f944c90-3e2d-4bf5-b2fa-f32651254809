"""Shared authentication models for cross-service compatibility."""
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class SessionInfo(BaseModel):
    """Session information returned by session validation.
    This recreates the same class object in get_session endpoint response
    """

    issuer: str
    subject: str
    expiration_access_utc: datetime = Field(alias="expirationAccessUtc")
    expiration_refresh_utc: datetime = Field(alias="expirationRefreshUtc")


class AuthContext(BaseModel):
    """Authentication context for validated sessions."""

    session_uuid: str
    subject: str
    issuer: str
    access_token: str
    session_info: Optional[SessionInfo] = None


# not sure this is fully needed right now/in the future
class AuthConfig(BaseModel):
    """Configuration for authentication services."""

    runtime_service_url: str = "http://qw-mono-dev-runtime:8000"
    session_validation_timeout: float = 10.0
    session_cache_ttl: int = 300  # 5 minutes
