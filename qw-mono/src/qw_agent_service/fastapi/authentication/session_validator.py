"""Shared session validation logic for both Falcon and FastAPI services."""
from typing import Optional
import httpx

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.fastapi.authentication.models import Auth<PERSON>ontext, AuthConfig, SessionInfo


class SessionValidator:
    """Validates sessions by calling existing Falcon authentication endpoints."""

    def __init__(self, config: AuthConfig, lf: LogFactory = NO_LOG_FACTORY):
        """
        Initialize the session validator with configuration.
        """
        self.config = config
        self.logger = lf.get_logger(__name__)
        # self._session_cache: Dict[str, AuthContext] = {}

    async def validate_session(self, session_token: str) -> Optional[AuthContext]:
        """
        Validate session token by calling existing Falcon session endpoint.
        """
        if not session_token:
            self.logger.warning("Empty session token provided")
            return None

        # TODO: Implement caching later
        # if session_token in self._session_cache:
        #     self.logger.info("Session found in cache")
        #     return self._session_cache[session_token]

        try:
            async with httpx.AsyncClient(timeout=self.config.session_validation_timeout) as client:
                # Call existing Falcon session validation endpoint
                response = await client.get(
                    f"{self.config.runtime_service_url}/api/v1/session",
                    cookies={"session": session_token}
                )

                if response.status_code == 200:
                    session_data = response.json()

                    # Create auth context from session data
                    auth_context = AuthContext(
                        session_uuid=session_token,  # Using token as UUID for now
                        subject=session_data.get("subject", ""),
                        issuer=session_data.get("issuer", ""),
                        access_token="",  # Not exposed in session endpoint
                        session_info=SessionInfo(**session_data)
                    )

                    # TODO: Implement caching later
                    # self._session_cache[session_token] = auth_context

                    return auth_context

                elif response.status_code == 401:
                    self.logger.warning("Session validation failed: unauthorized")
                    return None

                else:
                    self.logger.error(f"Session validation failed with status: {response.status_code}")
                    return None

        except httpx.TimeoutException:
            self.logger.error("Session validation timed out")
            return None
        except httpx.RequestError as e:
            self.logger.error(f"Session validation request failed: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error during session validation: {e}")
            return None

    # TODO: Implement caching later
    # def clear_cache(self) -> None:
    #     """Clear the session cache."""
    #     self._session_cache.clear()
    #     self.logger.info("Session cache cleared")

    @classmethod
    def from_config(cls, config: AuthConfig, lf: LogFactory = NO_LOG_FACTORY) -> "SessionValidator":
        """
        Create SessionValidator from configuration.
        """
        return cls(config=config, lf=lf)
