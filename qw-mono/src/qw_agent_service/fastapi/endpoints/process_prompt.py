from fastapi import <PERSON><PERSON><PERSON><PERSON>, HTT<PERSON>Exception, Depends, Request

from qw_agent_service.fastapi.authentication.authentication_service import get_current_user
from qw_agent_service.agent.agent_service import AgentService
from qw_agent_service.agent.models import (
    AgentPromptRequest,
    AgentPromptResponse,
)
from qw_agent_service.fastapi.authentication.models import AuthContext

router = APIRouter()

@router.post("/api/v1/agent/process-prompt", response_model=AgentPromptResponse)
async def process_agent_prompt(
    request: AgentPromptRequest,
    http_request: Request,
    current_user: AuthContext = Depends(get_current_user)
) -> AgentPromptResponse:
    """
    Process agent prompt with async handling.
    This endpoint maintains compatibility with the existing Falcon implementation.
    """
    # Get dependencies from request state
    log_factory = http_request.app.state.log_factory
    logger = log_factory.get_logger(__name__)
    logger.info(f"Processing agent prompt for user: {current_user.subject}")

    try:
        # Get agent service from app state
        service: AgentService = http_request.app.state.agent_service

        # Extract session token from request for pass-through authentication
        session_token = http_request.cookies.get("session")
        if session_token:
            logger.info(f"Extracted session token for pass-through: {session_token[:10]}...")
            # Add session token to request context
            if request.context is None:
                request.context = {}
            request.context["session_token"] = session_token
        else:
            logger.warning("No session token found in request cookies")

        # Process the prompt (session token is now in request.context)
        response = await service._process_prompt_async(request)

        logger.info(f"Agent prompt processed successfully for session: {response.session_id}")
        return response

    except Exception as e:
        logger.error(f"Error processing agent prompt: {e}")
        raise HTTPException(status_code=500, detail="Failed to process agent prompt")
