# AI Agent Architecture Convergence Plan

## Executive Summary

This document outlines the detailed convergence plan for merging two AI agent architecture implementations in the `qw_agent_service` module:

- **Legacy System**: `qw_agent_service/agent/` - Embedded agent system with service injection
- **MCP Prototype**: `qw_agent_service/agents/` - Model Context Protocol implementation with external tool server

**Objective**: Preserve the existing `agent/` architecture while incorporating MCP capabilities, then remove the `agents/` directory entirely.

## Architecture Analysis

### Legacy System (`agent/` directory)

**Structure:**
```
agent/
├── __init__.py
├── agent_service.py          # Main service entry point
├── models.py                 # Pydantic models for requests/responses
├── service_provider.py       # Service injection container
├── agents/
│   ├── base.py              # BaseAgent abstract class
│   ├── router_agent/
│   │   └── router_agent.py  # Main router agent
│   └── specialized_agents/
│       ├── document_intelligence_agent.py
│       ├── inspection_plan_builder_agent.py
│       └── registration.py
├── session/
│   └── session_service.py   # Session management
└── tools/
    ├── base.py
    ├── common_tools/        # Shared tools (get_context, get_available_actions)
    └── special_tools/       # Specialized tools (PMI, drawing analysis)
```

**Key Characteristics:**
- Service injection via `ServiceProvider` class
- Comprehensive agent registry system
- Structured tool organization
- Session management with in-memory storage
- Full pydantic-ai integration with typed responses
- Synchronous wrapper for async operations

### MCP Prototype (`agents/` directory)

**Structure:**
```
agents/
├── service.py                        # FastAPI service implementation
├── models.py                         # Simplified request/response models
├── minimal_agent.py                  # Basic text-only agent
├── mcp_enhanced_agent.py             # Router with MCP integration
└── document_intelligence_mcp_agent.py # MCP-based document agent
```

**Key Characteristics:**
- FastMCP client integration for external tool access
- Session token pass-through for tenant context
- Streamlined agent architecture
- Direct MCP tool calling capabilities
- Async-first design

## Feature Gap Analysis

### Missing in Legacy System

1. **MCP Client Integration**
   - FastMCP client setup and management
   - Session token pass-through to MCP server
   - Dynamic MCP tool discovery and calling

2. **Session Token Management**
   - Authentication token extraction from requests
   - Token propagation to external services
   - MCP client reconfiguration with new tokens

3. **External Tool Access**
   - Direct calling of MCP server tools
   - Tool discovery and listing capabilities
   - Error handling for external tool failures

### Missing in MCP Prototype

1. **Comprehensive Agent Registry**
   - Global agent registration system
   - Agent discovery and delegation mechanisms

2. **Service Injection Architecture**
   - ServiceProvider pattern for internal services
   - Inspection plan builder agent functionality

3. **Structured Tool Organization**
   - Common tools (get_context, get_available_actions)
   - Special tools (PMI management, drawing analysis)

4. **Robust Session Management**
   - Full ChatSession model with message history
   - Session timeout and cleanup mechanisms

## Convergence Strategy

### Phase 1: Enhance Legacy Router Agent with MCP Capabilities

**Objective**: Add MCP client integration to the existing `RouterAgent` while preserving all current functionality.

**Changes Required:**

1. **Update `agent/agents/router_agent/router_agent.py`**
   - Add FastMCP client initialization
   - Implement session token management
   - Add MCP tool discovery and calling capabilities
   - Preserve existing delegation to specialized agents

2. **Create `agent/clients/mcp_client.py`**
   - Encapsulate MCP client management
   - Handle session token updates
   - Provide tool discovery and calling interfaces

### Phase 2: Enhance Document Intelligence Agent with MCP Tools

**Objective**: Replace service injection in `DocumentIntelligenceAgent` with MCP tool access while maintaining the same interface.

**Changes Required:**

1. **Update `agent/agents/specialized_agents/document_intelligence_agent.py`**
   - Add MCP client dependency injection
   - Replace direct service calls with MCP tool calls
   - Maintain existing tool interface for backward compatibility

### Phase 3: Update FastAPI Integration

**Objective**: Modify FastAPI endpoints to use the enhanced legacy system instead of the prototype.

**Changes Required:**

1. **Update `fastapi/endpoints/process_prompt.py`**
   - Import from `agent.agent_service` instead of `agents.service`
   - Use legacy models from `agent.models`
   - Maintain session token extraction and pass-through

2. **Update `fastapi/app.py` and `fastapi/server.py`**
   - Initialize legacy `AgentService` instead of prototype service
   - Configure service provider with required dependencies

### Phase 4: Remove Prototype Directory

**Objective**: Clean up the codebase by removing the `agents/` directory entirely.

## Detailed Implementation Steps

### Step 1: Create MCP Client Module

**File**: `agent/clients/__init__.py`
```python
# Empty file for module initialization
```

**File**: `agent/clients/mcp_client.py`
```python
"""MCP client management for agent tools."""
from typing import Any, Dict, List, Optional
import httpx
from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

from qw_log_interface import NO_LOG_FACTORY, LogFactory


class MCPClientManager:
    """Manages MCP client connections and tool access."""

    def __init__(
        self,
        mcp_server_url: str,
        session_token: Optional[str] = None,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        self.mcp_server_url = mcp_server_url.rstrip('/mcp').rstrip('/')
        self.session_token = session_token
        self.logger = lf.get_logger(__name__)
        self.lf = lf

        self.mcp_client: Optional[Client] = None
        self.mcp_tools_available = False
        self.available_mcp_tools: List[str] = []

    async def initialize(self) -> bool:
        """Initialize MCP client connection."""
        try:
            if not await self._check_mcp_server_health():
                self.logger.warning("MCP server not available")
                return False

            self.mcp_client = self._create_mcp_client()
            await self._discover_mcp_tools()

            self.logger.info("MCP client initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize MCP client: {e}")
            return False

    def set_session_token(self, token: str) -> None:
        """Update session token and recreate client."""
        self.session_token = token
        if self.mcp_client:
            self.mcp_client = self._create_mcp_client()
            self.logger.info(f"MCP client updated with new session token: {token[:10]}...")

    async def list_tools(self) -> List[str]:
        """List available MCP tools."""
        if not self.mcp_client:
            return []

        try:
            async with self.mcp_client:
                tools = await self.mcp_client.list_tools()
                return [tool.name for tool in tools]
        except Exception as e:
            self.logger.error(f"Failed to list MCP tools: {e}")
            return []

    async def call_tool(self, tool_name: str, arguments: Optional[Dict[str, Any]] = None) -> str:
        """Call an MCP tool by name."""
        if not self.mcp_client:
            return "MCP client not available"

        try:
            async with self.mcp_client:
                result = await self.mcp_client.call_tool(tool_name, arguments or {})
                if result and len(result) > 0:
                    content = result[0]
                    if hasattr(content, 'text'):
                        return str(getattr(content, 'text', ''))
                    else:
                        return str(content)
                return f"Tool {tool_name} executed successfully"
        except Exception as e:
            self.logger.error(f"Error calling MCP tool {tool_name}: {e}")
            return f"Error executing tool {tool_name}: {str(e)}"

    def _create_mcp_client(self) -> Client:
        """Create MCP client with optional authentication."""
        if self.session_token:
            transport = StreamableHttpTransport(
                url=self.mcp_server_url,
                headers={"X-Session-Token": self.session_token}
            )
            return Client(transport)
        else:
            return Client(self.mcp_server_url)

    async def _check_mcp_server_health(self) -> bool:
        """Check if MCP server is available."""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.mcp_server_url}/")
                return response.status_code in [200, 406]
        except Exception as e:
            self.logger.info(f"MCP server health check failed: {e}")
            return False

    async def _discover_mcp_tools(self) -> None:
        """Discover available tools from MCP server."""
        try:
            if not self.mcp_client:
                return

            async with self.mcp_client:
                tools = await self.mcp_client.list_tools()
                self.logger.info(f"Discovered {len(tools)} MCP tools: {[tool.name for tool in tools]}")
                self.mcp_tools_available = len(tools) > 0
                self.available_mcp_tools = [tool.name for tool in tools]

        except Exception as e:
            self.logger.error(f"Failed to discover MCP tools: {e}")
            self.mcp_tools_available = False
            self.available_mcp_tools = []
```

### Step 2: Enhance RouterAgent with MCP Capabilities

**File**: `agent/agents/router_agent/router_agent.py`

**Changes**:
1. Add MCP client initialization
2. Add session token management tools
3. Add MCP tool calling capabilities
4. Preserve existing delegation functionality

**Key additions**:
```python
# Add imports
from typing import Optional
from qw_agent_service.agent.clients.mcp_client import MCPClientManager

# Add to __init__
self.mcp_client_manager: Optional[MCPClientManager] = None
self.session_token: Optional[str] = None

# Add initialization method
async def initialize_mcp_client(self, mcp_server_url: str) -> None:
    """Initialize MCP client for external tool access."""
    self.mcp_client_manager = MCPClientManager(
        mcp_server_url=mcp_server_url,
        session_token=self.session_token,
        lf=self.lf
    )
    await self.mcp_client_manager.initialize()

# Add session token management
async def set_session_token(self, token: str) -> None:
    """Set session token for MCP authentication."""
    self.session_token = token
    if self.mcp_client_manager:
        self.mcp_client_manager.set_session_token(token)

# Add MCP tools to _register_tools
@self.agent.tool
async def call_mcp_tool(ctx: RunContext[AgentContext], tool_name: str, arguments: Optional[Dict[str, Any]] = None) -> str:
    """Call an MCP tool by name with arguments."""
    if not self.mcp_client_manager:
        return "MCP client not available"
    return await self.mcp_client_manager.call_tool(tool_name, arguments)

@self.agent.tool
async def list_available_mcp_tools(ctx: RunContext[AgentContext]) -> str:
    """List all available MCP tools."""
    if not self.mcp_client_manager:
        return "MCP client not available"
    tools = await self.mcp_client_manager.list_tools()
    if tools:
        return f"Available MCP tools: {', '.join(tools)}"
    return "No MCP tools available"

@self.agent.tool
async def set_session_token_tool(ctx: RunContext[AgentContext], token: str) -> str:
    """Set session token for API authentication."""
    await self.set_session_token(token)
    return f"Session token configured successfully"
```

### Step 3: Update Document Intelligence Agent

**File**: `agent/agents/specialized_agents/document_intelligence_agent.py`

**Changes**:
1. Add MCP client dependency injection
2. Replace service-based tools with MCP tool calls
3. Maintain existing interface

**Key modifications**:
```python
# Add to imports
from typing import Optional
from qw_agent_service.agent.clients.mcp_client import MCPClientManager

# Add to __init__
self.mcp_client_manager: Optional[MCPClientManager] = None

# Add initialization method
def set_mcp_client_manager(self, mcp_client_manager: MCPClientManager) -> None:
    """Set MCP client manager for external tool access."""
    self.mcp_client_manager = mcp_client_manager

# Update _register_tools to include MCP tools
@self.agent.tool
async def call_mcp_tool(ctx: RunContext[AgentContext], tool_name: str, arguments: Optional[Dict[str, Any]] = None) -> str:
    """Call an MCP tool for document analysis."""
    if not self.mcp_client_manager:
        return "MCP client not available"
    return await self.mcp_client_manager.call_tool(tool_name, arguments)

@self.agent.tool
async def list_available_mcp_tools(ctx: RunContext[AgentContext]) -> str:
    """List all available MCP tools."""
    if not self.mcp_client_manager:
        return "MCP client not available"
    tools = await self.mcp_client_manager.list_tools()
    if tools:
        tool_descriptions = []
        for tool in tools:
            tool_descriptions.append(f"- {tool}")
        return f"Available MCP tools:\n" + "\n".join(tool_descriptions)
    return "No MCP tools available"

# Keep existing service-based tools as fallback
# Update system prompt to mention both MCP and service tools
```

**Updated system prompt**:
```python
def _get_system_prompt(self) -> str:
    return """
    You are a specialized assistant for answering questions about files associated with a single order line.
    You have access to both internal service tools and external MCP tools for comprehensive document analysis.

    You can help users:
    1. Check all files associated with the current order line
    2. Retrieve analysis for files labeled as technical drawing or material certificate
    3. Compare information across different files to identify inconsistencies or relationships

    Available tools:
    - get_context: Get the current context of frontend
    - get_available_actions: Get available frontend actions
    - call_mcp_tool: Call external MCP tools for advanced document analysis
    - list_available_mcp_tools: List all available MCP tools
    - technical_drawing_analysis_tool: Get technical drawing analysis (internal service)
    - material_certificate_analysis_tool: Get material certificate analysis (internal service)

    For document analysis:
    1. First get the context to understand what files are available
    2. Use list_available_mcp_tools to see what external analysis tools are available
    3. Use call_mcp_tool for advanced document analysis when MCP tools are available
    4. Fall back to internal service tools if MCP tools are unavailable
    5. Always provide clear, helpful responses about the file analysis results

    Focus on cross-document analysis and identifying relationships or inconsistencies between files.
    Always be helpful and provide actionable insights based on the file analysis.
    """
```

### Step 4: Update Agent Service

**File**: `agent/agent_service.py`

**Changes**:
1. Add MCP client initialization
2. Add session token management
3. Update router agent configuration

**Key additions**:
```python
# Add to imports
from typing import Optional

# Add to __init__
self.mcp_server_url = "http://mcp.docker.localhost"
self.session_token: Optional[str] = None

# Add to _configure_agent_service after router agent configuration
async def _initialize_mcp_integration(self) -> None:
    """Initialize MCP integration for router agent."""
    try:
        if hasattr(self.router_agent, 'initialize_mcp_client'):
            await self.router_agent.initialize_mcp_client(self.mcp_server_url)
            self.logger.info("MCP integration initialized successfully")

        # Initialize MCP client for document intelligence agent
        from qw_agent_service.agent.agents.base import get_agent_registry
        agent_registry = get_agent_registry(self.lf)
        doc_agent = agent_registry.get_agent("document_intelligence_agent")

        if doc_agent and hasattr(doc_agent, 'set_mcp_client_manager'):
            if hasattr(self.router_agent, 'mcp_client_manager'):
                doc_agent.set_mcp_client_manager(self.router_agent.mcp_client_manager)
                self.logger.info("MCP client manager set for document intelligence agent")

    except Exception as e:
        self.logger.error(f"Failed to initialize MCP integration: {e}")

# Add session token management
async def set_session_token(self, token: str) -> None:
    """Set session token for MCP authentication."""
    self.session_token = token
    if hasattr(self.router_agent, 'set_session_token'):
        await self.router_agent.set_session_token(token)
        self.logger.info(f"Session token set for router agent: {token[:10]}...")

# Update _process_prompt_async to handle session tokens
async def _process_prompt_async(self, request: AgentPromptRequest) -> AgentPromptResponse:
    """Process a user prompt asynchronously and generate a response with appropriate actions."""
    if not self.agent_available:
        self.logger.error("Agent service not available. Cannot process prompt.")
        session_id = request.session_id or uuid4()
        return self._build_response(message="Agent service is currently unavailable.", session_id=session_id)

    # Extract and set session token from context
    if request.context and "session_token" in request.context:
        session_token = request.context["session_token"]
        await self.set_session_token(session_token)
        self.logger.info("Session token extracted and set for MCP authentication")

    session = self.session_manager.get_or_create_session(request.session_id)

    try:
        # Initialize MCP integration if not already done
        if not hasattr(self, '_mcp_initialized'):
            await self._initialize_mcp_integration()
            self._mcp_initialized = True

        # Continue with existing logic...
        context: AgentContext = {
            "session_id": str(session.session_id),
            "user_context": request.context or {},
        }

        if request.context:
            self.logger.info(f"Context keys received from frontend: {list(request.context.keys())}")
        else:
            self.logger.warning("No context received from frontend")

        # Run the router agent
        response_data = await self.router_agent.process(
            request.prompt,
            context=cast(Dict[str, Any], context),
            usage=self.usage,
        )

        # Update the session activity
        self.session_manager.update_session_activity(session.session_id)

        # Log the response type
        self.logger.info(f"Agent response type: {type(response_data)}")

        # Handle different response types (existing logic continues...)
        if isinstance(response_data, AgentActionResponse):
            self.logger.info(f"Agent returned action response with {len(response_data.actions)} actions")
            for i, action in enumerate(response_data.actions):
                self.logger.info(
                    f"Action {i+1}: {action.action_type} on {action.component} with parameters: {action.parameters}"
                )
            return self._build_response(
                message=response_data.message,
                session_id=session.session_id,
                actions=response_data.actions,
            )
        elif isinstance(response_data, AgentTextResponse):
            self.logger.info("Agent returned text response")
            return self._build_response(
                message=response_data.message,
                session_id=session.session_id,
            )
        elif hasattr(response_data, "error"):  # AgentErrorResponse
            self.logger.error(f"Agent returned error response: {response_data.error}")
            return self._build_response(
                message=f"Error: {response_data.error}",
                session_id=session.session_id,
            )
        else:
            # This should never happen due to type checking
            return self._build_response(
                message="Unexpected response type from agent.",
                session_id=session.session_id,
            )
    except Exception as e:
        self.logger.error(f"Error processing prompt with agent: {str(e)}")
        return self._build_response(
            message="Sorry, I encountered an error processing your request.",
            session_id=session.session_id,
        )
```

### Step 5: Update FastAPI Integration

**File**: `fastapi/endpoints/process_prompt.py`

**Changes**:
1. Import from legacy agent system
2. Update model imports
3. Maintain session token extraction

```python
# Update imports
from qw_agent_service.agent.agent_service import AgentService
from qw_agent_service.agent.models import (
    AgentPromptRequest,
    AgentPromptResponse,
)

# The rest of the file remains the same - session token extraction logic is preserved
```

**File**: `fastapi/app.py`

**Changes**:
1. Initialize legacy AgentService
2. Configure ServiceProvider with dependencies

```python
# Update imports
from qw_agent_service.agent.agent_service import AgentService
from qw_agent_service.agent.service_provider import ServiceProvider

# Update service initialization in create_app or similar function
def create_agent_service(config: AgentServiceConfig, service_provider: ServiceProvider, lf: LogFactory) -> AgentService:
    """Create and configure the legacy agent service."""
    return AgentService(
        api_key=config.openai_api_key,
        service_provider=service_provider,
        lf=lf
    )
```

### Step 6: Update Service Provider

**File**: `agent/service_provider.py`

**Changes**:
1. Add MCP client manager to service provider
2. Update agent initialization to include MCP capabilities

```python
# Add to imports
from typing import Any, Optional
from qw_agent_service.agent.clients.mcp_client import MCPClientManager

# Add to __init__
mcp_client_manager: Optional[MCPClientManager] = None,

# Add to constructor
self._mcp_client_manager = mcp_client_manager

# Add getter
def get_mcp_client_manager(self) -> Optional[MCPClientManager]:
    """Get the MCP client manager."""
    return self._mcp_client_manager

def set_mcp_client_manager(self, mcp_client_manager: MCPClientManager) -> None:
    """Set the MCP client manager."""
    self._mcp_client_manager = mcp_client_manager
```

### Step 7: Update Agent Registration

**File**: `agent/agents/specialized_agents/registration.py`

**Changes**:
1. Update document intelligence agent registration to include MCP client setup

```python
# Add to register_specialized_agents function
def register_specialized_agents(
    api_key: str,
    service_provider: ServiceProvider,
    lf: LogFactory = NO_LOG_FACTORY,
) -> None:
    """Register all specialized agents with the global registry."""
    agent_registry = get_agent_registry(lf)
    logger = lf.get_logger(__name__)

    try:
        # Register document intelligence agent
        doc_agent = DocumentIntelligenceAgent(
            api_key=api_key,
            service_provider=service_provider,
            lf=lf,
        )

        # Set MCP client manager if available
        mcp_client_manager = service_provider.get_mcp_client_manager()
        if mcp_client_manager and hasattr(doc_agent, 'set_mcp_client_manager'):
            doc_agent.set_mcp_client_manager(mcp_client_manager)
            logger.info("MCP client manager set for document intelligence agent")

        agent_registry.register_agent(doc_agent)
        logger.info("Document intelligence agent registered successfully")

        # Register inspection plan builder agent (unchanged)
        inspection_agent = InspectionPlanBuilderAgent(
            api_key=api_key,
            lf=lf,
        )
        agent_registry.register_agent(inspection_agent)
        logger.info("Inspection plan builder agent registered successfully")

    except Exception as e:
        logger.error(f"Failed to register specialized agents: {e}")
        raise
```

### Step 8: Remove Prototype Directory

**Actions**:
1. Delete `agents/` directory entirely
2. Update any remaining imports
3. Clean up configuration references

```bash
# Remove the prototype directory
rm -rf qw-mono/src/qw_agent_service/agents/

# Search for any remaining imports from the agents directory
grep -r "from qw_agent_service.agents" qw-mono/src/
grep -r "import qw_agent_service.agents" qw-mono/src/

# Update any found references to use the legacy agent system
```


### Implementation Phase

#### Phase 1: MCP Client Infrastructure
- [ ] Create `agent/clients/__init__.py`
- [ ] Create `agent/clients/mcp_client.py` with MCPClientManager class

#### Phase 2: Router Agent Enhancement
- [ ] Update `agent/agents/router_agent/router_agent.py` with MCP capabilities
- [ ] Add session token management methods
- [ ] Add MCP tool calling capabilities
- [ ] Test router agent with MCP integration
- [ ] Verify existing delegation functionality preserved

#### Phase 3: Document Intelligence Agent Enhancement
- [ ] Update `agent/agents/specialized_agents/document_intelligence_agent.py`
- [ ] Add MCP client dependency injection
- [ ] Add MCP tool calling methods
- [ ] Update system prompt to include MCP tools

#### Phase 4: Agent Service Updates
- [ ] Update `agent/agent_service.py` with MCP initialization
- [ ] Add session token management
- [ ] Add MCP integration initialization

#### Phase 5: Service Provider Updates
- [ ] Update `agent/service_provider.py` with MCP client manager
- [ ] Update `agent/agents/specialized_agents/registration.py`

#### Phase 6: FastAPI Integration
- [ ] Update `fastapi/endpoints/process_prompt.py` imports
- [ ] Update `fastapi/app.py` service initialization

### Cleanup Phase
- [ ] Remove `agents/` directory completely
- [ ] Update imports and references
- [ ] Clean up configuration files
- [ ] Update documentation
- [ ] Remove unused dependencies

### Post-Migration Verification
- [ ] All existing functionality preserved
- [ ] MCP tools accessible through router agent
- [ ] Session token authentication working
- [ ] Document intelligence agent using MCP tools
- [ ] FastAPI endpoints responding correctly
- [ ] No references to prototype `agents/` directory

## Risk Mitigation

### Backward Compatibility
- **Strategy**: Preserve all existing interfaces and method signatures

### Error Handling
- **Strategy**: Graceful degradation when MCP server unavailable
- **Monitoring**: Surgical logging for errors

### Performance Impact
- **Strategy**: Minimize performance overhead from MCP integration
- **Implementation**: Lazy initialization of MCP client, connection pooling

### Testing Strategy
- **Unit Tests**: MCP client manager, enhanced router agent, updated document intelligence agent
- **Integration Tests**: Agent service with MCP integration, FastAPI endpoints
- **End-to-End Tests**: Complete request flow with session token authentication
- **Performance Tests**: Session token management, MCP tool calling latency
