"""
Session management for agent conversations.
"""
import time
from typing import Dict, Optional
from uuid import UUI<PERSON>

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.agent.models import ChatSession


class SessionService:
    """Manages chat sessions for agent conversations."""

    # Session timeout in seconds (30 minutes)
    SESSION_TIMEOUT = 30 * 60

    def __init__(self, lf: LogFactory = NO_LOG_FACTORY):
        """
        Initialize the session manager.

        Args:
            lf: Logger factory
        """
        self.logger = lf.get_logger(__name__)

        # Dictionary to store active chat sessions
        self.sessions: Dict[UUID, ChatSession] = {}

    def create_new_session(self) -> UUID:
        """
        Create a new chat session.

        Returns:
            The ID of the newly created session
        """
        session = ChatSession()
        self.sessions[session.session_id] = session
        self.logger.info(f"Explicitly created new chat session: {session.session_id}")
        return session.session_id

    def delete_session(self, session_id: UUID) -> bool:
        """
        Delete a chat session.

        Args:
            session_id: The ID of the session to delete

        Returns:
            True if the session was deleted, False if it didn't exist
        """
        if session_id in self.sessions:
            del self.sessions[session_id]
            self.logger.info(f"Deleted chat session: {session_id}")
            return True
        return False

    def get_or_create_session(self, session_id: Optional[UUID] = None) -> ChatSession:
        """
        Get an existing session or create a new one.

        Args:
            session_id: Optional ID of an existing session

        Returns:
            A ChatSession object
        """
        # Clean up expired sessions
        self._cleanup_expired_sessions()

        # If no session ID provided or session doesn't exist, create a new one
        if session_id is None or session_id not in self.sessions:
            session = ChatSession()
            self.sessions[session.session_id] = session
            self.logger.info(f"Created new chat session: {session.session_id}")
            return session

        # Update last activity time for existing session
        session = self.sessions[session_id]
        session.last_activity = time.time()
        return session

    def update_session_activity(self, session_id: UUID) -> None:
        """
        Update the last activity timestamp for a session.

        Args:
            session_id: The ID of the session to update
        """
        if session_id in self.sessions:
            self.sessions[session_id].last_activity = time.time()
            self.logger.info(f"Updated activity for session: {session_id}")

    def _cleanup_expired_sessions(self) -> None:
        """Remove expired sessions from the sessions dictionary."""
        current_time = time.time()
        expired_sessions = [
            session_id
            for session_id, session in self.sessions.items()
            if current_time - session.last_activity > self.SESSION_TIMEOUT
        ]

        for session_id in expired_sessions:
            self.logger.info(f"Removing expired session: {session_id}")
            del self.sessions[session_id]
