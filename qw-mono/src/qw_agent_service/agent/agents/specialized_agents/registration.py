"""
Registration functions for specialized agents.
"""

from qw_agent_service.agent.agents.base import get_agent_registry
from qw_agent_service.agent.agents.specialized_agents.document_intelligence_agent import DocumentIntelligenceAgent
from qw_agent_service.agent.agents.specialized_agents.inspection_plan_builder_agent import InspectionPlanBuilderAgent
from qw_agent_service.agent.service_provider import ServiceProvider
from qw_log_interface import NO_LOG_FACTORY, LogFactory


def register_specialized_agents(
    api_key: str,
    service_provider: ServiceProvider,
    lf: LogFactory = NO_LOG_FACTORY,
) -> None:
    """
    Register specialized agents with the global registry.

    Args:
        api_key: The API key for the LLM
        service_provider: The service provider for accessing services
        lf: The log factory
    """
    registry = get_agent_registry(lf)
    logger = lf.get_logger(__name__)

    try:
        # Register the inspection plan builder agent
        inspection_agent = InspectionPlanBuilderAgent(
            api_key=api_key,
            lf=lf,
        )
        registry.register_agent(inspection_agent)
        logger.info("Inspection plan builder agent registered successfully")

        # Register the document intelligence agent (MCP-only)
        doc_agent = DocumentIntelligenceAgent(
            api_key=api_key,
            lf=lf,
        )

        # Set MCP client manager if available
        mcp_client_manager = service_provider.get_mcp_client_manager()
        if mcp_client_manager and hasattr(doc_agent, "set_mcp_client_manager"):
            doc_agent.set_mcp_client_manager(mcp_client_manager)
            logger.info("MCP client manager set for document intelligence agent")

        registry.register_agent(doc_agent)
        logger.info("Document intelligence agent registered successfully")

    except Exception as e:
        logger.error(f"Failed to register specialized agents: {e}")
        raise
