"""
Agent specialized for inspection plan tasks.
"""

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.agent.agents.base import BaseAgent
from qw_agent_service.agent.models import AgentContext
from qw_agent_service.agent.tools.common_tools.get_available_actions_tool import get_available_actions
from qw_agent_service.agent.tools.common_tools.get_context_tool import get_context
from qw_agent_service.agent.tools.special_tools.add_pmis_to_plan import add_pmis_to_plan


class InspectionPlanBuilderAgent(BaseAgent[AgentContext]):
    """Agent specialized for inspection plan tasks."""

    def __init__(
        self,
        api_key: str,
        model_name: str = "gpt-4.1",
        temperature: float = 0.2,
        timeout: float = 30.0,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the inspection plan agent.
        """
        super().__init__(
            name="inspection_plan_builder_agent",
            description="Agent specialized for inspection plan tasks",
            api_key=api_key,
            model_name=model_name,
            temperature=temperature,
            timeout=timeout,
            lf=lf,
        )

    def _get_system_prompt(self) -> str:
        """
        Get the system prompt for the inspection plan agent.
        """
        return """
        You are a specialized assistant for working with inspection plans and PMIs
        (Product and Manufacturing Information).

        Available tools:
        - add_pmis_to_plan: Add PMIs to the inspection plan by providing a list of PMI IDs
        - get_context: Get the current context
        - get_available_actions: Get the list of available actions

        When adding PMIs to a plan:
        1. Examine PMIs in user_context.viewerDrawing.pmis
        2. Filter PMIs based on the user's request (e.g., diameters, linear measurements)
        3. Extract pmiId values into a list of integers
        4. Call add_pmis_to_plan with parameters={"pmi_ids": [list of IDs]}
        5. Return an AgentActionResponse with the action and a descriptive message

        Example:
        ```
        # Tool call
        function: add_pmis_to_plan
        parameters:
          pmi_ids: [1, 2, 3, 4]

        # Response format
        {
          "type": "AgentActionResponse",
          "message": "All diameter PMIs have been added to the plan.",
          "actions": [
            {
              "action_type": "addPmisToPlan",
              "component": "viewerDrawing",
              "parameters": {
                "pmiIds": [1, 2, 3, 4]
              }
            }
          ]
        }
        ```

        Always return an AgentActionResponse when using action tools, not just a text response.
        """

    def _register_tools(self) -> None:
        """
        Register tools for the inspection plan agent.
        """
        # Register common tools
        self.agent.tool(get_context)
        self.agent.tool(get_available_actions)

        self.agent.tool(add_pmis_to_plan)
