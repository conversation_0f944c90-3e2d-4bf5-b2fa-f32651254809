"""
Agent specialized for cross document auditing tasks using MCP tools.
"""
from typing import Any, Dict, Optional

from pydantic_ai import <PERSON><PERSON>ontext

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.agent.agents.base import BaseAgent
from qw_agent_service.agent.models import Agent<PERSON>ontext
from qw_agent_service.agent.clients.mcp_client import MCPClientManager
from qw_agent_service.agent.tools.common_tools.get_available_actions_tool import get_available_actions
from qw_agent_service.agent.tools.common_tools.get_context_tool import get_context


class DocumentIntelligenceAgent(BaseAgent[AgentContext]):
    """Agent specialized for cross document auditing tasks using MCP tools."""

    def __init__(
        self,
        api_key: str,
        model_name: str = "gpt-4.1",
        temperature: float = 0.2,
        timeout: float = 30.0,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the document intelligence agent.

        Args:
            api_key: The API key for the LLM
            model_name: The name of the LLM model to use
            temperature: The temperature for the LLM
            timeout: The timeout for the LLM
            lf: The log factory
        """
        super().__init__(
            name="document_intelligence_agent",
            description="Agent specialized for cross document auditing tasks using MCP tools",
            api_key=api_key,
            model_name=model_name,
            temperature=temperature,
            timeout=timeout,
            lf=lf,
        )

        # Initialize MCP client manager
        self.mcp_client_manager: Optional[MCPClientManager] = None

    def set_mcp_client_manager(self, mcp_client_manager: MCPClientManager) -> None:
        """Set MCP client manager for external tool access."""
        self.mcp_client_manager = mcp_client_manager
        self.logger.info("MCP client manager set for document intelligence agent")

    def _get_system_prompt(self) -> str:
        """
        Get the system prompt for the document intelligence agent.
        """
        return """
        You are a specialized assistant for answering questions about files associated with a single order line.
        You use external MCP tools to access file information and perform document analysis.

        You can help users:
        1. Check all files associated with the current order line
        2. Retrieve analysis for files labeled as technical drawing or material certificate
        3. Compare information across different files to identify inconsistencies or relationships

        Available tools:
        - get_context: Get the current context of frontend
        - get_available_actions: Get available frontend actions
        - call_mcp_tool: Call external MCP tools for document analysis
        - list_available_mcp_tools: List all available MCP tools

        When working with files:
        1. First get the context to understand what files are available.
        2. Use list_available_mcp_tools to see what external analysis tools are available
        3. Use call_mcp_tool for document analysis (e.g., technical drawings, material certificates)
        4. Always provide clear, helpful responses about the file analysis results

        The context contains information about the current view:
        - fileResources: List of files in the current order. A file object contains details about revisions.
        - viewedFileResource: Currently viewed file resource if any.
        - orderLineId: Current order line ID
        - revisionToResourceMap: Mapping of revision IDs to file resource information. Each entry contains:
          - fileResourceId: The ID of the file resource
          - fileResourceName: The display name of the file resource
          - fileResourceLabel: The label of the file resource

        Focus on cross-document analysis and identifying relationships or inconsistencies between files.
        Always be helpful and provide actionable insights based on the file analysis.
        """

    def _register_tools(self) -> None:
        """
        Register tools for the document intelligence agent.
        """
        # Register common tools
        self.agent.tool(get_context)
        self.agent.tool(get_available_actions)

        # Register MCP tools
        self.agent.tool(self._call_mcp_tool)
        self.agent.tool(self._list_available_mcp_tools)

    async def _call_mcp_tool(self, ctx: RunContext[AgentContext], tool_name: str, arguments: Optional[Dict[str, Any]] = None) -> str:
        """Call an MCP tool for document analysis."""
        if not self.mcp_client_manager:
            return "MCP client not available"
        return await self.mcp_client_manager.call_tool(tool_name, arguments)

    async def _list_available_mcp_tools(self, ctx: RunContext[AgentContext]) -> str:
        """List all available MCP tools."""
        if not self.mcp_client_manager:
            return "MCP client not available"

        tool_descriptions = await self.mcp_client_manager.get_tool_descriptions()
        if tool_descriptions:
            tool_list = []
            for tool in tool_descriptions:
                tool_list.append(f"- {tool['name']}: {tool['description']}")
            return f"Available MCP tools ({len(tool_descriptions)}):\n" + "\n".join(tool_list)
        else:
            return "No MCP tools available"
