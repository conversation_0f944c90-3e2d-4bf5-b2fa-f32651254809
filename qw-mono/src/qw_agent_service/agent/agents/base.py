"""
Base classes for specialized agents.
"""
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Any, Dict, Generic, List, Optional, TypeVar, Union, cast

from pydantic_ai import Agent as PydanticAgent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.settings import ModelSettings
from pydantic_ai.usage import Usage, UsageLimits

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.agent.models import (
    AgentActionResponse,
    AgentContext,
    AgentErrorResponse,
    AgentResponse,
    AgentTextResponse,
)

T = TypeVar("T")


class BaseAgent(Generic[T], ABC):
    """Base class for all specialized agents."""

    def __init__(
        self,
        name: str,
        description: str,
        api_key: str,
        model_name: str,
        temperature: float,
        timeout: float,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the agent.

        Args:
            name: The name of the agent
            description: The description of the agent
            api_key: The API key for the LLM
            model_name: The name of the LLM model to use
            temperature: The temperature for the LLM
            timeout: The timeout for the LLM
            lf: The log factory
        """
        self.name = name
        self.description = description
        self.api_key = api_key
        self.model_name = model_name
        self.temperature = temperature
        self.timeout = timeout
        self.logger = lf.get_logger(__name__)
        self.lf = lf

        # Configure the pydantic-ai agent
        self._configure_agent()

    def _configure_agent(self) -> None:
        """
        Configure the pydantic-ai agent.
        """
        try:
            # Prepare model settings
            model_settings = ModelSettings(
                temperature=self.temperature,
                timeout=self.timeout,
            )

            # Setup OpenAI model with provider
            provider = OpenAIProvider(api_key=self.api_key)

            # Check if we need to extract the model name without provider prefix
            model_name = self.model_name
            if model_name.startswith("openai:"):
                model_name = model_name.split(":")[-1]

            model = OpenAIModel(model_name, provider=provider)

            # Create the agent with the appropriate output type
            self.agent = PydanticAgent[AgentContext, Union[AgentActionResponse, AgentTextResponse, AgentErrorResponse]](
                model=model,
                model_settings=model_settings,
                retries=2,
                system_prompt=self._get_system_prompt(),
                output_type=Union[AgentActionResponse, AgentTextResponse, AgentErrorResponse],  # type: ignore
                deps_type=AgentContext,
            )

            # Register tools
            self._register_tools()

            self.agent_available = True
        except Exception as e:
            self.logger.error(f"Failed to configure agent {self.name}: {str(e)}")
            self.agent_available = False

    @abstractmethod
    def _get_system_prompt(self) -> str:
        """
        Get the system prompt for the agent.
        """
        pass

    @abstractmethod
    def _register_tools(self) -> None:
        """
        Register tools for the agent.
        """
        pass

    async def process(
        self, prompt: str, context: Optional[Dict[str, Any]] = None, usage: Optional[Usage] = None
    ) -> AgentResponse:
        """
        Process a prompt with the agent.
        """
        if not self.agent_available:
            self.logger.error(f"Agent {self.name} not available. Cannot process prompt.")
            return AgentErrorResponse(error="Agent service is currently unavailable.")

        try:
            # Prepare the context
            deps = cast(AgentContext, context or {})

            # Create usage limits with no request limit to prevent UsageLimitExceeded errors
            usage_limits = UsageLimits(request_limit=None)

            # self.logger.info(f"Agent {self.name} context keys: {list(deps.keys())}")

            # Run the agent with unlimited usage
            result = await self.agent.run(prompt, deps=deps, usage=usage, usage_limits=usage_limits)

            # Log the result details
            if result.output:
                self.logger.info(f"Agent {self.name} output type: {type(result.output)}")
                self.logger.info(f"Agent {self.name} output: {result.output}")
                # Return the output
                return result.output  # type: ignore[no-any-return]
            else:
                self.logger.error(f"Agent {self.name} returned unexpected result type: {type(result)}")
                return AgentErrorResponse(
                    error="Unexpected response format from agent.", details=f"Result type: {type(result)}"
                )
        except Exception as e:
            self.logger.error(f"Error processing prompt with agent {self.name}: {str(e)}")
            return AgentErrorResponse(
                error="Sorry, I encountered an error processing your request.",
                details=str(e),
            )


@dataclass
class AgentRegistry:
    """Registry for specialized agents."""

    agents: Dict[str, BaseAgent[Any]] = field(default_factory=dict)
    lf: LogFactory = field(default=NO_LOG_FACTORY)
    logger: Any = field(init=False, default=None)

    def __post_init__(self) -> None:
        self.logger = self.lf.get_logger(__name__)

    def register_agent(self, agent: BaseAgent[Any]) -> None:
        """
        Register an agent with the registry.
        """
        self.agents[agent.name] = agent
        self.logger.info(f"Registered agent: {agent.name}")

    def get_agent(self, name: str) -> Optional[BaseAgent[Any]]:
        """
        Get an agent by name.
        """
        return self.agents.get(name)

    def get_all_agents(self) -> List[BaseAgent[Any]]:
        """
        Get all registered agents.
        """
        return list(self.agents.values())

    def get_agent_names(self) -> List[str]:
        """
        Get the names of all registered agents.
        """
        return list(self.agents.keys())


# Global agent registry
_agent_registry: Optional[AgentRegistry] = None


def get_agent_registry(lf: LogFactory = NO_LOG_FACTORY) -> AgentRegistry:
    """
    Get the global agent registry.
    """
    global _agent_registry
    if _agent_registry is None:
        _agent_registry = AgentRegistry(lf=lf)
    return _agent_registry
