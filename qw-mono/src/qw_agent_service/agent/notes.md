# Agent Architecture Notes

### Agent Response Flow
- The pydantic-ai framework expects tools to return the same type that the agent is configured to output
- For our agent, this means tools should return `AgentActionResponse`, `AgentTextResponse`, or `AgentErrorResponse`
- When a tool returns an `AgentAction` or `AgentToolResponse`, the framework doesn't automatically convert it to the expected output type

### Tool Response Handling
- `ActionTool.run_tool` returns an `AgentToolResponse` with an `action_result` field
- `InformationTool.run_tool` returns an `AgentToolResponse` with an `info_result` field
- The intermediate `AgentToolResponse` representation provides a consistent interface for all tools
- However, this intermediate representation needs to be converted to the appropriate agent response type
- That's why when necessary
**Update the system prompt** to instruct the model to return an `AgentActionResponse` with the action
  - This is the least invasive approach
  - Allows for dynamic, contextual messages
  - Relies on the model following instructions correctly
