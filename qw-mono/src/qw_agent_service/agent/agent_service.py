"""
Agent service for processing user prompts and generating responses with actions.

This service implements a multi-level agentic architecture using pydantic-ai.
It serves as the entry point for agent interactions, dynamically selecting
appropriate tools and specialized agents based on the context.
"""

import asyncio
from typing import Any, Dict, List, Optional, cast
from uuid import UUID, uuid4

from pydantic_ai.usage import Usage

from qw_agent_service.agent.agents.router_agent.router_agent import RouterAgent
from qw_agent_service.agent.agents.specialized_agents.registration import register_specialized_agents
from qw_agent_service.agent.models import (
    AgentAction,
    AgentActionResponse,
    AgentContext,
    AgentPromptRequest,
    AgentPromptResponse,
    AgentTextResponse,
)
from qw_agent_service.agent.service_provider import ServiceProvider
from qw_agent_service.agent.session.session_service import SessionService
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class AgentService:
    """Service for processing agent prompts and generating responses with actions."""

    def __init__(
        self,
        api_key: str,
        service_provider: ServiceProvider,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        self.logger = lf.get_logger(__name__)
        self.api_key = api_key
        self.lf = lf
        self.service_provider = service_provider
        self.agent_available = False

        self.session_manager = SessionService(lf=lf)
        self.usage = Usage()

        # MCP integration
        self.mcp_server_url = "http://mcp.docker.localhost"
        self.session_token: Optional[str] = None
        self._mcp_initialized = False

        self._configure_agent_service()

    def _configure_agent_service(self) -> None:
        """
        Configure the agent service with specialized agents and tools.
        """
        try:
            register_specialized_agents(
                api_key=self.api_key,
                service_provider=self.service_provider,
                lf=self.lf,
            )

            self._configure_router_agent()

            self.agent_available = True
        except Exception as e:
            self.logger.error(f"Failed to configure agent service: {str(e)}")
            self.agent_available = False

    def _configure_router_agent(self) -> None:
        """
        Configure the main router agent that delegates to specialized agents.
        """
        try:
            self.router_agent = RouterAgent(
                api_key=self.api_key,
                lf=self.lf,
            )

            self.logger.info("Router agent configured successfully")
        except Exception as e:
            self.logger.error(f"Failed to configure router agent: {str(e)}")
            raise

    async def _initialize_mcp_integration(self) -> None:
        """Initialize MCP integration for router agent."""
        try:
            if hasattr(self.router_agent, "initialize_mcp_client"):
                await self.router_agent.initialize_mcp_client(self.mcp_server_url)
                self.logger.info("MCP integration initialized successfully")

            # Initialize MCP client for document intelligence agent
            from qw_agent_service.agent.agents.base import get_agent_registry

            agent_registry = get_agent_registry(self.lf)
            doc_agent = agent_registry.get_agent("document_intelligence_agent")

            if doc_agent and hasattr(doc_agent, "set_mcp_client_manager"):
                if hasattr(self.router_agent, "mcp_client_manager"):
                    # Use getattr to safely call the method
                    set_mcp_method = getattr(doc_agent, "set_mcp_client_manager", None)
                    if set_mcp_method and callable(set_mcp_method):
                        set_mcp_method(self.router_agent.mcp_client_manager)
                        self.logger.info("MCP client manager set for document intelligence agent")

        except Exception as e:
            self.logger.error(f"Failed to initialize MCP integration: {e}")

    async def set_session_token(self, token: str) -> None:
        """Set session token for MCP authentication."""
        self.session_token = token
        if hasattr(self.router_agent, "set_session_token"):
            await self.router_agent.set_session_token(token)
            self.logger.info(f"Session token set for router agent: {token[:10]}...")

    def process_prompt(self, request: AgentPromptRequest) -> AgentPromptResponse:
        """
        Synchronous wrapper for process_prompt_async.
        """

        # Create a new event loop if there isn't one
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        return loop.run_until_complete(self._process_prompt_async(request))

    async def _process_prompt_async(self, request: AgentPromptRequest) -> AgentPromptResponse:
        """
        Process a user prompt asynchronously and generate a response with appropriate actions.
        """
        if not self.agent_available:
            self.logger.error("Agent service not available. Cannot process prompt.")
            session_id = request.session_id or uuid4()
            return self._build_response(message="Agent service is currently unavailable.", session_id=session_id)

        # Extract and set session token from context
        if request.context and "session_token" in request.context:
            session_token = request.context["session_token"]
            await self.set_session_token(session_token)
            self.logger.info("Session token extracted and set for MCP authentication")

        session = self.session_manager.get_or_create_session(request.session_id)

        try:
            # Initialize MCP integration if not already done
            if not self._mcp_initialized:
                await self._initialize_mcp_integration()
                self._mcp_initialized = True

            # Prepare the context
            context: AgentContext = {
                "session_id": str(session.session_id),
                "user_context": request.context or {},
            }

            if request.context:
                self.logger.info(f"Context keys received from frontend: {list(request.context.keys())}")
            else:
                self.logger.warning("No context received from frontend")

            # Run the router agent
            response_data = await self.router_agent.process(
                request.prompt,
                context=cast(Dict[str, Any], context),
                usage=self.usage,
            )

            # Update the session activity
            self.session_manager.update_session_activity(session.session_id)

            # Log the response type
            self.logger.info(f"Agent response type: {type(response_data)}")

            # Handle different response types
            if isinstance(response_data, AgentActionResponse):
                self.logger.info(f"Agent returned action response with {len(response_data.actions)} actions")
                for i, action in enumerate(response_data.actions):
                    self.logger.info(
                        f"Action {i+1}: {action.action_type} on {action.component} with parameters: {action.parameters}"
                    )
                return self._build_response(
                    message=response_data.message,
                    session_id=session.session_id,
                    actions=response_data.actions,
                )
            elif isinstance(response_data, AgentTextResponse):
                self.logger.info("Agent returned text response")
                return self._build_response(
                    message=response_data.message,
                    session_id=session.session_id,
                )
            elif hasattr(response_data, "error"):  # AgentErrorResponse
                self.logger.error(f"Agent returned error response: {response_data.error}")
                return self._build_response(
                    message=f"Error: {response_data.error}",
                    session_id=session.session_id,
                )
            else:
                # This should never happen due to type checking
                return self._build_response(
                    message="Unexpected response type from agent.",
                    session_id=session.session_id,
                )
        except Exception as e:
            self.logger.error(f"Error processing prompt with agent: {str(e)}")
            return self._build_response(
                message="Sorry, I encountered an error processing your request.",
                session_id=session.session_id,
            )

    def _build_response(
        self, message: str, session_id: UUID, actions: Optional[List[AgentAction]] = None
    ) -> AgentPromptResponse:
        """
        Build a standardized agent prompt response.

        Args:
            message: The message to include in the response
            session_id: The session ID for the response
            actions: Optional list of actions to include

        Returns:
            A properly formatted AgentPromptResponse
        """
        return AgentPromptResponse(
            message=message,
            session_id=session_id,
            actions=actions,
        )
