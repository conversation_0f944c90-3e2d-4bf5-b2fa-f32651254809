#!/usr/bin/env python3
"""
Tiny CLI for hitting the /agent/process-prompt endpoint.

Examples
--------
# prompt + cookie on the command line
python agent_client.py -c 'SESSION123' -p 'hi'

# prompt from stdin, cookie from env var
export SESSION_COOKIE=SESSION123
echo "hi" | python agent_client.py
"""
from __future__ import annotations

import argparse
import json
import os
import sys
from urllib.parse import urlparse, urlunparse

import requests

DEFAULT_ENDPOINT = "http://app.docker.localhost/api/v1/agent/process-prompt"


# --------------------------------------------------------------------------- #
# helpers                                                                     #
# --------------------------------------------------------------------------- #
def _rewrite_localhost(url: str) -> tuple[str, dict[str, str]]:
    """
    If the hostname ends with `.localhost`, change the URL to 127.0.0.1
    but return an extra {"Host": original_hostname} header so the HTTP
    virtual-host logic still works (Traefik/Nginx inside Docker).
    """
    parsed = urlparse(url)
    if parsed.hostname and parsed.hostname.endswith(".localhost"):
        new_netloc = f"127.0.0.1{':' + str(parsed.port) if parsed.port else ''}"
        rewritten_url = urlunparse(parsed._replace(netloc=new_netloc))
        return rewritten_url, {"Host": parsed.hostname}
    return url, {}


def send_prompt(prompt: str, session_cookie: str, endpoint: str = DEFAULT_ENDPOINT) -> str:
    url, extra_hdrs = _rewrite_localhost(endpoint)

    headers = {
        "Content-Type": "application/json",
        **extra_hdrs,  # Host header if we rewrote
    }
    cookies = {"session": session_cookie}

    resp = requests.post(url, headers=headers, data=json.dumps({"prompt": prompt}), cookies=cookies, timeout=30)
    resp.raise_for_status()
    return resp.text


# --------------------------------------------------------------------------- #
# CLI                                                                         #
# --------------------------------------------------------------------------- #
def main() -> None:
    p = argparse.ArgumentParser(description="Call the prompt-processing API.")
    p.add_argument("-c", "--cookie", help="session cookie (or use $SESSION_COOKIE)")
    p.add_argument("-p", "--prompt", help="text to send; omit to read stdin")
    p.add_argument("-e", "--endpoint", default=DEFAULT_ENDPOINT, help=f"API URL (default: {DEFAULT_ENDPOINT})")
    args = p.parse_args()

    cookie = args.cookie or os.getenv("SESSION_COOKIE")
    if not cookie:
        sys.exit("Error: provide a session cookie with --cookie or $SESSION_COOKIE")

    prompt = args.prompt if args.prompt is not None else sys.stdin.read().rstrip("\n")
    if not prompt:
        sys.exit("Error: no prompt provided")

    try:
        print(send_prompt(prompt, cookie, args.endpoint))
    except requests.HTTPError as err:
        sys.exit(f"HTTP {err.response.status_code}: {err.response.text}")
    except requests.RequestException as err:
        sys.exit(f"Request failed: {err}")


if __name__ == "__main__":
    main()
