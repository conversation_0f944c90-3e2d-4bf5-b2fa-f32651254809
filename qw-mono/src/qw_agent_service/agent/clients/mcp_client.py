"""MCP client management for agent tools."""
from typing import Any, Dict, List, Optional
import httpx
from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

from qw_log_interface import NO_LOG_FACTORY, LogFactory


class MCPClientManager:
    """Manages MCP client connections and tool access."""

    def __init__(
        self,
        mcp_server_url: str,
        session_token: Optional[str] = None,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize MCP client manager.

        Args:
            mcp_server_url: URL of the MCP server
            session_token: Optional session token for authentication
            lf: Log factory for logging
        """
        self.mcp_server_url = mcp_server_url.rstrip('/mcp').rstrip('/')
        self.session_token = session_token
        self.logger = lf.get_logger(__name__)
        self.lf = lf

        self.mcp_client: Optional[Client[Any]] = None
        self.mcp_tools_available = False
        self.available_mcp_tools: List[str] = []

    async def initialize(self) -> bool:
        """Initialize MCP client connection."""
        try:
            if not await self._check_mcp_server_health():
                self.logger.warning("MCP server not available")
                return False

            self.mcp_client = self._create_mcp_client()
            await self._discover_mcp_tools()

            self.logger.info("MCP client initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize MCP client: {e}")
            return False

    def set_session_token(self, token: str) -> None:
        """Update session token and recreate client."""
        self.session_token = token
        if self.mcp_client:
            self.mcp_client = self._create_mcp_client()
            self.logger.info(f"MCP client updated with new session token: {token[:10]}...")

    async def list_tools(self) -> List[str]:
        """List available MCP tools."""
        if not self.mcp_client:
            return []

        try:
            async with self.mcp_client:
                tools = await self.mcp_client.list_tools()
                return [tool.name for tool in tools]
        except Exception as e:
            self.logger.error(f"Failed to list MCP tools: {e}")
            return []

    async def get_tool_descriptions(self) -> List[Dict[str, str]]:
        """Get detailed tool descriptions."""
        if not self.mcp_client:
            return []

        try:
            async with self.mcp_client:
                tools = await self.mcp_client.list_tools()
                return [
                    {
                        "name": tool.name,
                        "description": getattr(tool, 'description', 'No description available')
                    }
                    for tool in tools
                ]
        except Exception as e:
            self.logger.error(f"Failed to get tool descriptions: {e}")
            return []

    async def call_tool(self, tool_name: str, arguments: Optional[Dict[str, Any]] = None) -> str:
        """Call an MCP tool by name."""
        if not self.mcp_client:
            return "MCP client not available"

        try:
            async with self.mcp_client:
                result = await self.mcp_client.call_tool(tool_name, arguments or {})
                if result and len(result) > 0:
                    content = result[0]
                    if hasattr(content, 'text'):
                        return str(getattr(content, 'text', ''))
                    else:
                        return str(content)
                return f"Tool {tool_name} executed successfully"
        except Exception as e:
            self.logger.error(f"Error calling MCP tool {tool_name}: {e}")
            return f"Error executing tool {tool_name}: {str(e)}"

    def _create_mcp_client(self) -> Client[Any]:
        """Create MCP client with optional authentication."""
        if self.session_token:
            transport = StreamableHttpTransport(
                url=self.mcp_server_url,
                headers={"X-Session-Token": self.session_token}
            )
            return Client(transport)
        else:
            return Client(self.mcp_server_url)

    async def _check_mcp_server_health(self) -> bool:
        """Check if MCP server is available."""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.mcp_server_url}/")
                return response.status_code in [200, 406]
        except Exception as e:
            self.logger.info(f"MCP server health check failed: {e}")
            return False

    async def _discover_mcp_tools(self) -> None:
        """Discover available tools from MCP server."""
        try:
            if not self.mcp_client:
                return

            async with self.mcp_client:
                tools = await self.mcp_client.list_tools()
                self.logger.info(f"Discovered {len(tools)} MCP tools: {[tool.name for tool in tools]}")
                self.mcp_tools_available = len(tools) > 0
                self.available_mcp_tools = [tool.name for tool in tools]

        except Exception as e:
            self.logger.error(f"Failed to discover MCP tools: {e}")
            self.mcp_tools_available = False
            self.available_mcp_tools = []
