"""
Service provider for agent tools.

This module provides a service provider that gives access to services
without serialization issues.
"""
from typing import Any, Optional

from qw_agent_service.agent.clients.mcp_client import MC<PERSON>lientManager
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class ServiceProvider:
    """Provides access to services without serialization issues."""

    def __init__(
        self,
        drawing_analysis_client: Any = None,
        file_service: Any = None,
        s3_service: Any = None,
        material_certificate_service: Any = None,
        order_service: Any = None,
        inspection_plan_service: Any = None,
        material_service: Any = None,
        drawing_service: Any = None,
        mcp_client_manager: Optional[MCPClientManager] = None,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the service provider with all available services.

        Args:
            drawing_analysis_client: The technical drawing analysis client
            file_service: The file resource service
            s3_service: The S3 object service
            material_certificate_service: The material certificate service
            order_service: The order service
            inspection_plan_service: The inspection plan service
            material_service: The material service
            drawing_service: The drawing service
            mcp_client_manager: The MCP client manager for external tools
            lf: The log factory
        """
        self._drawing_analysis_client = drawing_analysis_client
        self._file_service = file_service
        self._s3_service = s3_service
        self._material_certificate_service = material_certificate_service
        self._order_service = order_service
        self._inspection_plan_service = inspection_plan_service
        self._material_service = material_service
        self._drawing_service = drawing_service
        self._mcp_client_manager = mcp_client_manager
        self._lf = lf
        self._logger = lf.get_logger(__name__)

    def get_drawing_analysis_client(self) -> Any:
        """Get the technical drawing analysis client."""
        return self._drawing_analysis_client

    def get_file_service(self) -> Any:
        """Get the file resource service."""
        return self._file_service

    def get_s3_service(self) -> Any:
        """Get the S3 object service."""
        return self._s3_service

    def get_material_certificate_service(self) -> Any:
        """Get the material certificate service."""
        return self._material_certificate_service

    def get_order_service(self) -> Any:
        """Get the order service."""
        return self._order_service

    def get_inspection_plan_service(self) -> Any:
        """Get the inspection plan service."""
        return self._inspection_plan_service

    def get_material_service(self) -> Any:
        """Get the material service."""
        return self._material_service

    def get_drawing_service(self) -> Any:
        """Get the drawing service."""
        return self._drawing_service

    def get_mcp_client_manager(self) -> Optional[MCPClientManager]:
        """Get the MCP client manager."""
        return self._mcp_client_manager

    def set_mcp_client_manager(self, mcp_client_manager: MCPClientManager) -> None:
        """Set the MCP client manager."""
        self._mcp_client_manager = mcp_client_manager
        self._logger.info("MCP client manager set in service provider")
