"""
Base classes and interfaces for agent tools.
"""
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Any, Dict, Generic, List, Optional, TypeVar

from pydantic_ai import RunContext

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.agent.models import AgentAction, AgentToolResponse

T = TypeVar("T")


class AgentTool(Generic[T], ABC):
    """Base class for all agent tools."""

    def __init__(self, name: str, description: str, lf: LogFactory = NO_LOG_FACTORY):
        """
        Initialize the tool.
        """
        self.name = name
        self.description = description
        self.logger = lf.get_logger(__name__)

    @abstractmethod
    async def run_tool(self, ctx: RunContext[T], **kwargs: Any) -> AgentToolResponse:
        """
        Execute the tool with the given context and parameters.
        """
        pass


class ActionTool(AgentTool[T], ABC):
    """Base class for tools that generate frontend actions."""

    @abstractmethod
    async def create_action(self, ctx: RunContext[T], **kwargs: Any) -> AgentAction:
        """
        Create an action to be executed by the frontend.
        """
        pass

    async def run_tool(self, ctx: RunContext[T], **kwargs: Any) -> AgentToolResponse:
        """
        Execute the tool and return an action.
        """
        action = await self.create_action(ctx, **kwargs)
        return AgentToolResponse(action_result=action)


class InformationTool(AgentTool[T], ABC):
    """Base class for tools that retrieve information."""

    @abstractmethod
    async def get_information(self, ctx: RunContext[T], **kwargs: Any) -> Any:
        """
        Retrieve information based on the given parameters.
        """
        pass

    async def run_tool(self, ctx: RunContext[T], **kwargs: Any) -> AgentToolResponse:
        """
        Execute the tool and return the retrieved information.
        """
        info = await self.get_information(ctx, **kwargs)
        return AgentToolResponse(info_result=info)


@dataclass
class ToolRegistry:
    """Registry for agent tools."""

    tools: Dict[str, AgentTool[Any]] = field(default_factory=dict)
    lf: LogFactory = field(default=NO_LOG_FACTORY)
    logger: Any = field(init=False, default=None)

    def __post_init__(self) -> None:
        self.logger = self.lf.get_logger(__name__)

    def register_tool(self, tool: AgentTool[Any]) -> None:
        """
        Register a tool with the registry.
        """
        self.tools[tool.name] = tool
        self.logger.info(f"Registered tool: {tool.name}")

    def get_tool(self, name: str) -> Optional[AgentTool[Any]]:
        """
        Get a tool by name.
        """
        return self.tools.get(name)

    def get_all_tools(self) -> List[AgentTool[Any]]:
        """
        Get all registered tools.
        """
        return list(self.tools.values())

    def get_tool_names(self) -> List[str]:
        """
        Get the names of all registered tools.
        """
        return list(self.tools.keys())


# Global tool registry
_tool_registry: Optional[ToolRegistry] = None


def get_tool_registry(lf: LogFactory = NO_LOG_FACTORY) -> ToolRegistry:
    """
    Get the global tool registry.
    """
    global _tool_registry
    if _tool_registry is None:
        _tool_registry = ToolRegistry(lf=lf)
    return _tool_registry
