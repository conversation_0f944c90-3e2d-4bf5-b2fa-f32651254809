"""
Tool for adding PMIs to an inspection plan.
"""
from typing import Any, Dict, List, cast

from pydantic_ai import <PERSON><PERSON>ontext

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.agent.models import AgentAction, AgentContext
from qw_agent_service.agent.tools.base import ActionTool


class AddPmisToPlanTool(ActionTool[AgentContext]):
    """Tool that adds PMIs to an inspection plan."""

    def __init__(
        self,
        name: str = "addPmisToPlan",
        description: str = "Add PMIs to the inspection plan by providing a list of PMI IDs",
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the tool.

        Args:
            name: The name of the tool
            description: The description of the tool
            lf: The log factory
        """
        super().__init__(name, description, lf)

    async def create_action(self, ctx: RunContext[AgentContext], **kwargs: Any) -> AgentAction:
        """
        Create an action to add PMIs to a plan.

        Args:
            ctx: The run context
            **kwargs: Keyword arguments including pmi_ids (List[int]) - List of PMI IDs to add to the plan

        Returns:
            An action to add PMIs to the plan
        """
        self.logger.info(f"AddPmisToPlanTool.create_action called with kwargs: {kwargs}")

        # Get pmi_ids from kwargs, checking multiple possible locations
        pmi_ids: List[int] = []

        if "parameters" in kwargs and isinstance(kwargs["parameters"], dict):
            parameters = cast(Dict[str, Any], kwargs["parameters"])
            self.logger.info(f"Parameters found in kwargs: {parameters}")

            if "pmi_ids" in parameters:
                pmi_ids = cast(List[int], parameters["pmi_ids"])
                self.logger.info(f"Found pmi_ids in parameters: {pmi_ids}")
            else:
                self.logger.warning(f"No 'pmi_ids' key found in parameters. Available keys: {list(parameters.keys())}")
        else:
            self.logger.warning(
                f"No 'parameters' key found in kwargs or it's not a dict. Keys in kwargs: {list(kwargs.keys())}"
            )

        if not pmi_ids:
            self.logger.warning("No PMI IDs found to add to plan")

        self.logger.info(f"Creating action to add PMIs to plan: {pmi_ids}")
        action = AgentAction(action_type="addPmisToPlan", component="viewerDrawing", parameters={"pmiIds": pmi_ids})
        self.logger.info(f"Created action: {action}")
        return action


# Wrapper function for direct use with pydantic-ai Agent
async def add_pmis_to_plan(ctx: RunContext[AgentContext], **kwargs: Any) -> AgentAction:
    """
    Add PMIs to the inspection plan.

    Args:
        ctx: The run context
        **kwargs: Keyword arguments including:
            parameters: Dictionary containing:
                pmi_ids: List of PMI IDs to add to the plan

    Returns:
        An action to add PMIs to the plan
    """
    logger = NO_LOG_FACTORY.get_logger(__name__)
    logger.info(f"add_pmis_to_plan wrapper function called with kwargs: {kwargs}")

    # Create the tool
    tool = _create_add_pmis_to_plan_tool(lf=NO_LOG_FACTORY)

    # Call the tool's create_action method
    action = await tool.create_action(ctx, **kwargs)

    # Log the result with detailed type information
    logger.info(f"add_pmis_to_plan wrapper function returning action: {action} (type: {type(action)})")
    logger.info(
        f"Action details - type: {action.action_type}, "
        f"component: {action.component}, "
        f"parameters: {action.parameters} (type: {type(action.parameters)})"
    )

    return action


def _create_add_pmis_to_plan_tool(lf: LogFactory = NO_LOG_FACTORY) -> AddPmisToPlanTool:
    """
    Create a new instance of the add PMIs to plan tool.

    Args:
        lf: The log factory

    Returns:
        A new AddPmisToPlanTool instance
    """
    return AddPmisToPlanTool(lf=lf)
