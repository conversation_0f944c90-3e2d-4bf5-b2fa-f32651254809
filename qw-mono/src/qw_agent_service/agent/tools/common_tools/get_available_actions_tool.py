"""
Tool for retrieving available actions.
"""
from typing import Any, List

from pydantic_ai import Run<PERSON>ontext

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.agent.models import Agent<PERSON>ontext
from qw_agent_service.agent.tools.base import InformationTool


class AvailableActionsTool(InformationTool[AgentContext]):
    """Tool that retrieves information about available actions."""

    def __init__(
        self,
        name: str = "get_available_actions",
        description: str = "Get the list of available actions",
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the tool.

        Args:
            name: The name of the tool
            description: The description of the tool
            lf: The log factory
        """
        super().__init__(name, description, lf)

    async def get_information(self, ctx: RunContext[AgentContext], **kwargs: Any) -> List[str]:
        """
        Retrieve the list of available actions.

        Args:
            ctx: The run context containing the agent context
            **kwargs: Additional keyword arguments

        Returns:
            A list of available action names
        """
        if "available_actions" in ctx.deps:
            return ctx.deps["available_actions"]
        return []


def create_available_actions_tool(lf: LogFactory = NO_LOG_FACTORY) -> AvailableActionsTool:
    """
    Create a new instance of the available actions tool.

    Args:
        lf: The log factory

    Returns:
        A new AvailableActionsTool instance
    """
    return AvailableActionsTool(lf=lf)


# Wrapper function for direct use with pydantic-ai Agent
async def get_available_actions(ctx: RunContext[AgentContext], **kwargs: Any) -> List[str]:
    """
    Get the list of available actions.

    Args:
        ctx: The run context
        **kwargs: Additional keyword arguments

    Returns:
        A list of available action names
    """
    tool = create_available_actions_tool(lf=NO_LOG_FACTORY)
    return await tool.get_information(ctx, **kwargs)
