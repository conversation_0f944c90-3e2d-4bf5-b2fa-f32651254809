"""
Tool for retrieving context information.
"""
from typing import Any, Dict

from pydantic_ai import <PERSON><PERSON><PERSON>x<PERSON>

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.agent.models import Agent<PERSON>ontext
from qw_agent_service.agent.tools.base import InformationTool


class ContextTool(InformationTool[AgentContext]):
    """Tool that retrieves information from the agent context."""

    def __init__(
        self,
        name: str = "get_context",
        description: str = "Get the current context of the agent",
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the tool.

        Args:
            name: The name of the tool
            description: The description of the tool
            lf: The log factory
        """
        super().__init__(name, description, lf)

    async def get_information(self, ctx: RunContext[AgentContext], **kwargs: Any) -> Dict[str, Any]:
        """
        Retrieve the current context.

        Args:
            ctx: The run context containing the agent context
            **kwargs: Additional keyword arguments

        Returns:
            A dictionary containing the serializable parts of the agent context
        """
        try:
            # Get a copy of the context
            context = dict(ctx.deps)

            # Filter out non-serializable objects (like functions)
            filtered_context = {}

            # Always include user_context if available
            if "user_context" in context:
                filtered_context["user_context"] = context["user_context"]

            # Include session_id if available
            if "session_id" in context:
                filtered_context["session_id"] = context["session_id"]

            # Include available_actions if available
            if "available_actions" in context:
                filtered_context["available_actions"] = context["available_actions"]

            # Log what we're returning
            self.logger.info(f"Returning filtered context with keys: {list(filtered_context.keys())}")

            return filtered_context

        except Exception as e:
            self.logger.error(f"Error filtering context: {str(e)}")
            # Return a minimal context to avoid errors
            return {"error": f"Failed to retrieve context: {str(e)}"}


def create_context_tool(lf: LogFactory = NO_LOG_FACTORY) -> ContextTool:
    """
    Create a new instance of the context tool.

    Args:
        lf: The log factory

    Returns:
        A new ContextTool instance
    """
    return ContextTool(lf=lf)


# Wrapper function for direct use with pydantic-ai Agent
async def get_context(ctx: RunContext[AgentContext], **kwargs: Any) -> Dict[str, Any]:
    """
    Get the current context of the agent.

    Args:
        ctx: The run context
        **kwargs: Additional keyword arguments

    Returns:
        A dictionary containing the agent context
    """
    tool = create_context_tool(lf=NO_LOG_FACTORY)
    return await tool.get_information(ctx, **kwargs)
