ARG BUILD_VERSION="0.0.0.0"
ARG BUILD_COMMIT="0000000000000000000000000000000000000000"

FROM ubuntu:22.04 AS base

ARG USER_ID=1000
ARG GROUP_ID=1000
RUN addgroup --gid $GROUP_ID qualiwise && adduser --system --uid $USER_ID --ingroup qualiwise qualiwise

USER qualiwise
WORKDIR /home/<USER>/

USER root

# base (ubuntu:22.04)
#  ├── base-python
#  │    └── base-dev
#  │         └── build-qw-webui-angular
#  ├── build-qw-mono
#  └── build-worker

FROM base AS base-python

RUN apt update && \
  apt install -y python3 libpq-dev python3-dev build-essential curl libgl1-mesa-glx libglib2.0-0 libcairo2 libpango-1.0-0 libharfbuzz0b libpangoft2-1.0-0 libgdk-pixbuf2.0-0 && \
  curl https://bootstrap.pypa.io/pip/get-pip.py -o /opt/get-pip.py && \
  python3 /opt/get-pip.py && \
  rm /opt/get-pip.py

USER qualiwise
COPY qw-mono/requirements.txt /home/<USER>
RUN pip install --user -r /home/<USER>/requirements.txt
ENV PATH="$PATH:/home/<USER>/.local/bin"


FROM base-python AS base-dev

USER root
RUN apt install -y git less nano xz-utils
RUN curl https://nodejs.org/dist/v20.16.0/node-v20.16.0-linux-x64.tar.xz -o /opt/node.tar.xz && \
  tar -C /opt -xJf /opt/node.tar.xz && \
  ln -s /opt/node-v20.16.0-linux-x64 /opt/node && \
  rm /opt/node.tar.xz
ENV PATH="$PATH:/opt/node/bin"
USER qualiwise

COPY qw-mono/requirements-dev.txt /home/<USER>
RUN pip install --user -r /home/<USER>/requirements-dev.txt
RUN npm install -g --silent \
  @angular/cli@18.2.14 \
  prettier@3.5.2 \
  prettier-plugin-tailwindcss@0.5.14 \
  @tailwindcss/forms@0.5.7 \
  @trivago/prettier-plugin-sort-imports@5.2.2


FROM base-dev AS build-qw-webui-angular

RUN mkdir /home/<USER>/qw-webui
COPY qw-webui/src /home/<USER>/qw-webui/src
COPY qw-webui/tsconfig* /home/<USER>/qw-webui
COPY qw-webui/package* /home/<USER>/qw-webui
COPY qw-webui/angular.json /home/<USER>/qw-webui
COPY qw-webui/ngsw-config.json /home/<USER>/qw-webui
COPY qw-webui/tailwind.config.ts /home/<USER>/qw-webui

USER root
RUN cd qw-webui && \
  npm install && \
  ng build qw-webui -c production --localize


FROM base AS build-qw-mono
ARG BUILD_VERSION
ARG BUILD_COMMIT

ENV BUILD_VERSION=$BUILD_VERSION
ENV BUILD_COMMIT=$BUILD_COMMIT

RUN apt update && apt install -y python3 libpq-dev libgl1-mesa-glx libglib2.0-0 libcairo2 libpango-1.0-0 libharfbuzz0b libpangoft2-1.0-0 libgdk-pixbuf2.0-0
COPY --from=base-python /home/<USER>/.local /home/<USER>/.local
USER qualiwise

RUN mkdir -p /home/<USER>/qw-mono/src
COPY --chown=$USER_ID:$GROUP_ID --chmod=700 qw-mono/src /home/<USER>/qw-mono/src
RUN rm -rf /home/<USER>/qw-mono/src/qw_*_test

RUN mkdir -p /home/<USER>/qw-mono/config
COPY --chown=$USER_ID:$GROUP_ID --chmod=600 qw-mono/config/app.yaml /home/<USER>/qw-mono/config/app.yaml

WORKDIR /home/<USER>/qw-mono/src
ENTRYPOINT ["python3", "entrypoint.py", "--qw-mono-config", "/home/<USER>/qw-mono/config/app.yaml"]


FROM base-python AS build-worker
ARG BUILD_VERSION
ARG BUILD_COMMIT

ENV BUILD_VERSION=$BUILD_VERSION
ENV BUILD_COMMIT=$BUILD_COMMIT

USER root
RUN apt update && apt install -y python3 libpq-dev libgl1-mesa-glx libglib2.0-0
USER qualiwise

COPY qw-mono/requirements.txt /home/<USER>
RUN pip install --user -r /home/<USER>/requirements.txt
ENV PATH="$PATH:/home/<USER>/.local/bin"

# Copy qw-mono source code since worker depends on it
RUN mkdir -p /home/<USER>/qw-mono/src
COPY --chown=$USER_ID:$GROUP_ID --chmod=700 qw-mono/src /home/<USER>/qw-mono/src
RUN rm -rf /home/<USER>/qw-mono/src/qw_*_test

RUN mkdir -p /home/<USER>/qw-mono/config
COPY --chown=$USER_ID:$GROUP_ID --chmod=600 qw-mono/config/app.yaml /home/<USER>/qw-mono/config/app.yaml

WORKDIR /home/<USER>/qw-mono/src
# Start worker using config-based entrypoint
ENTRYPOINT ["python3", "-m", "qw_worker", "--qw-mono-config", "/home/<USER>/qw-mono/config/app.yaml"]

# Agent Service Build Stage
FROM base-python AS build-agent-service
ARG BUILD_VERSION
ARG BUILD_COMMIT

ENV BUILD_VERSION=$BUILD_VERSION
ENV BUILD_COMMIT=$BUILD_COMMIT

# Install git for development (needed for some Python packages)
USER root
RUN apt update && apt install -y git
USER qualiwise

# Agent service uses base-python (no npm needed)
# This ensures we have Python dependencies without frontend tools

# MCP Server Build Stage
FROM base-python AS build-mcp-server
ARG BUILD_VERSION
ARG BUILD_COMMIT

ENV BUILD_VERSION=$BUILD_VERSION
ENV BUILD_COMMIT=$BUILD_COMMIT

# Install git for development (needed for some Python packages)
USER root
RUN apt update && apt install -y git
USER qualiwise

# Copy and set up the development entrypoint script
COPY scripts/dev_mcp_entrypoint.sh /usr/local/bin/dev_mcp_entrypoint.sh
RUN chmod +x /usr/local/bin/dev_mcp_entrypoint.sh
ENTRYPOINT ["/usr/local/bin/dev_mcp_entrypoint.sh"]

FROM nginx:1.27.5-alpine3.21-slim AS build-qw-webui
ARG BUILD_VERSION
ARG BUILD_COMMIT

ENV BUILD_VERSION=$BUILD_VERSION
ENV BUILD_COMMIT=$BUILD_COMMIT

ENV SERVER_NAME="localhost"
ENV SERVER_PORT=80
ENV SERVER_WORKER_COUNT="auto"

ENV NGINX_ENVSUBST_TEMPLATE_SUFFIX=".template"
ENV NGINX_ENVSUBST_VARS="SERVER_NAME SERVER_PORT SERVER_WORKER_COUNT"

# see https://hub.docker.com/_/nginx
COPY --from=build-qw-webui-angular /home/<USER>/qw-webui/dist/qw-webui/browser /usr/share/nginx/html
RUN echo "{\"version\": \"$BUILD_VERSION\", \"commit\": \"$BUILD_COMMIT\"}" > /usr/share/nginx/html/info
RUN mkdir /etc/nginx/templates
COPY qw-webui/nginx.conf /etc/nginx/templates/nginx.conf.template
ENV NGINX_ENVSUBST_OUTPUT_DIR="/etc/nginx"

CMD ["nginx", "-g", "daemon off;"]
